import requests
from bs4 import BeautifulSoup
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.oxml.ns import qn
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import re
from io import BytesIO
import os
from PIL import Image
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 创建输出目录
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'output')
os.makedirs(OUTPUT_DIR, exist_ok=True)

def sanitize_filename(filename):
    """
    清理文件名，移除不合法的字符。
    Args:
        filename (str): 原始文件名。
    Returns:
        str: 清理后的合法文件名。
    """
    return re.sub(r'[\/*?"<>|]', "", filename).strip()

def process_code_block(doc, element):
    """
    专门处理代码块，保持代码格式和缩进。

    Args:
        doc: Word文档对象
        element: BeautifulSoup元素（pre、code等）
    """
    # 获取代码文本，保持原始格式
    code_text = ""

    # 处理不同类型的代码标签
    if element.name == 'pre':
        # 对于pre标签，需要保持内部的所有格式
        for content in element.contents:
            if hasattr(content, 'name'):
                if content.name == 'br':
                    code_text += '\n'
                elif content.name == 'code':
                    # 递归处理code标签内容
                    code_text += extract_code_text(content)
                else:
                    code_text += content.get_text()
            else:
                # 文本节点
                code_text += str(content)
    elif element.name == 'code':
        code_text = extract_code_text(element)

    # 清理代码文本
    code_text = code_text.strip()
    if not code_text:
        return

    # 创建代码段落
    para = doc.add_paragraph()
    run = para.add_run(code_text)

    # 设置代码字体和样式
    font = run.font
    font.name = 'Consolas'
    font.size = Pt(10)  # 设置字体大小
    font.color.rgb = RGBColor(0, 0, 0)  # 黑色字体

    # 设置中文字体
    rpr = run._element.get_or_add_rPr()
    rpr.rFonts.set(qn('w:eastAsia'), 'Consolas')

    # 设置段落样式
    para.style = 'Intense Quote'
    para.alignment = WD_PARAGRAPH_ALIGNMENT.LEFT

    # 设置段落背景色（通过样式）
    try:
        para_format = para.paragraph_format
        para_format.left_indent = Inches(0.2)  # 左缩进
        para_format.right_indent = Inches(0.2)  # 右缩进
        para_format.space_before = Pt(6)  # 段前间距
        para_format.space_after = Pt(6)   # 段后间距
    except:
        pass  # 如果设置失败，继续执行

def is_advertisement_content(element):
    """
    检测元素是否为广告内容。

    Args:
        element: BeautifulSoup元素

    Returns:
        bool: 如果是广告内容返回True，否则返回False
    """
    if not element:
        return False

    # 获取元素文本内容
    text = element.get_text(strip=True).lower()

    # 代码块保护 - 如果是代码相关元素，不判定为广告
    if element.name in ['pre', 'code']:
        return False

    # 强技术内容保护 - 包含明确的代码特征
    code_patterns = [
        'def ', 'class ', 'import ', 'from ', 'return ', 'print(',
        'console.log', 'function(', 'var ', 'let ', 'const ',
        '#!/', '<?php', '<html>', '<script>', 'SELECT ', 'INSERT ',
        'git clone', 'docker run', 'npm install', 'pip install'
    ]

    for pattern in code_patterns:
        if pattern in text:
            return False

    # 技术文章标题保护
    if element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
        tech_title_keywords = [
            '教程', '入门', '指南', '实践', '原理', '解析', '分析',
            '技术', '算法', '数据结构', '编程', '开发', '实现'
        ]
        # 标题如果包含技术词汇且不包含明显广告词，则保护
        has_tech_word = any(keyword in text for keyword in tech_title_keywords)
        has_strong_ad = any(keyword in text for keyword in ['限时', '优惠', '购买', '报名', '扫码'])
        if has_tech_word and not has_strong_ad:
            return False

    # 强广告关键词 - 单独出现就判定为广告
    strong_ad_keywords = [
        # 明确的推广词
        '限时优惠', '优惠券', '立即购买', '马上购买', '点击购买', '在线购买',
        '免费试用', '免费领取', '限时免费', '特价', '打折', '促销',
        '秒杀', '团购', '拼团', '砍价', '返现', '红包',

        # 明确的引流词
        '扫码关注', '长按识别', '识别二维码', '关注公众号', '点击关注',
        '加微信', '添加微信', '进群', '入群', '群聊',

        # 明确的营销词
        '点击链接', '复制链接', '打开链接', '访问链接',
        '下载app', '安装app', '立即注册', '马上注册',
        '商务合作', '品牌合作', '推荐产品', '产品推荐'
    ]

    # 弱广告关键词 - 需要结合上下文判断
    weak_ad_keywords = [
        '推广', '广告', '赞助', '特别推荐',
        '购买', '下单', '付费', '收费', '咨询'
    ]

    # 上下文敏感的关键词 - 需要更严格的判断
    context_sensitive_keywords = [
        '注册', '登录', '会员', 'vip', '价格', '费用', '合作'
    ]

    # 课程相关词 - 需要更严格的条件
    course_ad_keywords = [
        '课程报名', '立即报名', '马上报名', '招生中', '火热招生',
        '直播课', '录播课', '在线课程', '付费课程', 'vip课程'
    ]

    # 检查强广告关键词
    for keyword in strong_ad_keywords:
        if keyword in text:
            return True

    # 检查课程广告关键词
    for keyword in course_ad_keywords:
        if keyword in text:
            return True

    # 检查弱广告关键词 - 需要更严格的条件
    weak_keyword_count = 0
    found_weak_keywords = []
    for keyword in weak_ad_keywords:
        if keyword in text:
            weak_keyword_count += 1
            found_weak_keywords.append(keyword)

    # 检查上下文敏感关键词
    context_keyword_count = 0
    for keyword in context_sensitive_keywords:
        if keyword in text:
            context_keyword_count += 1

    # 更严格的弱广告词判断
    if weak_keyword_count >= 2:
        # 如果包含多个弱广告关键词，进一步检查上下文
        # 排除技术讨论的情况
        tech_context_words = ['系统', '设计', '实现', '原理', '方法', '技术', '算法', '功能']
        has_tech_context = any(word in text for word in tech_context_words)

        if not has_tech_context:
            return True
    elif weak_keyword_count >= 1 and len(text) < 50:
        # 非常短的文本且包含弱广告词
        return True
    elif context_keyword_count >= 1:
        # 对于上下文敏感的关键词，需要更严格的条件
        # 只有在明确的营销上下文中才判定为广告
        marketing_context = ['立即', '马上', '快速', '免费', '优惠', '特价', '限时']
        has_marketing_context = any(word in text for word in marketing_context)

        if has_marketing_context and len(text) < 100:
            return True

    # 检查CSS类名和ID
    class_attr = ' '.join(element.get('class', [])).lower()
    id_attr = element.get('id', '').lower()

    ad_class_indicators = [
        'ad', 'ads', 'advertisement', 'promo', 'promotion', 'sponsor',
        'banner', 'commercial', 'marketing', 'affiliate'
    ]

    for indicator in ad_class_indicators:
        if indicator in class_attr or indicator in id_attr:
            return True

    # 检查样式属性
    style_attr = element.get('style', '').lower()
    if 'display:none' in style_attr.replace(' ', '') or 'visibility:hidden' in style_attr.replace(' ', ''):
        return True

    # 检查特定的HTML结构特征
    # 1. 包含大量链接的段落（可能是导航或推广）- 更严格的条件
    links = element.find_all('a')
    if len(links) > 5 and len(text) < 150:  # 提高链接数量阈值，降低文本长度阈值
        # 进一步检查链接文本是否包含广告词
        link_texts = [link.get_text().lower() for link in links]
        ad_link_count = 0
        for link_text in link_texts:
            if any(keyword in link_text for keyword in ['购买', '下载', '注册', '关注', '加群']):
                ad_link_count += 1
        if ad_link_count >= 2:  # 至少2个链接包含广告词才判定
            return True

    # 2. 包含二维码相关的图片
    images = element.find_all('img')
    for img in images:
        img_alt = img.get('alt', '').lower()
        img_src = img.get('src', '').lower()
        if any(qr_word in img_alt or qr_word in img_src for qr_word in ['qr', 'qrcode', '二维码', 'erweima']):
            return True

    # 3. 单独的img标签检查
    if element.name == 'img':
        img_alt = element.get('alt', '').lower()
        img_src = element.get('src', '').lower()
        if any(qr_word in img_alt or qr_word in img_src for qr_word in ['qr', 'qrcode', '二维码', 'erweima']):
            return True

    return False

def should_stop_processing(element):
    """
    检查是否应该停止处理后续内容（遇到文章结束标记）。

    Args:
        element: BeautifulSoup元素

    Returns:
        bool: 如果应该停止处理返回True
    """
    if not element:
        return False

    text = element.get_text(strip=True)

    # 常见的文章结束标记 - 更精确的匹配，避免与广告检测冲突
    stop_markers = [
        '在上传至交流群的文件中',
        '本文完',
        '全文完',
        '--- 完 ---',
        '(完)',
        '【完】',
        '往期推荐',
        '往期精彩',
        '相关阅读',
        '推荐阅读',
        '延伸阅读',
        '更多精彩',
        '点击阅读原文',
        '阅读原文',
        '版权声明',
        '免责声明',
        '声明：本文',
        '来源：',
        '转载请注明',
        '如有侵权',
        '侵权删除'
    ]

    # 检查精确匹配的结束标记
    for marker in stop_markers:
        if marker in text:
            return True

    # 检查特定的结束模式（更严格的条件）
    # 只有当文本很短且主要是这些内容时才认为是结束标记
    if len(text) < 100:  # 短文本
        end_patterns = [
            '关注我们',
            '联系我们',
            '商务合作',
            '投稿邮箱'
        ]
        for pattern in end_patterns:
            if pattern in text and len(text) < 50:  # 更严格的条件
                return True

    return False

def extract_code_text(element):
    """
    从代码元素中提取文本，保持格式。

    Args:
        element: BeautifulSoup元素

    Returns:
        str: 提取的代码文本
    """
    text = ""
    for content in element.contents:
        if hasattr(content, 'name'):
            if content.name == 'br':
                text += '\n'
            elif content.name in ['span', 'div', 'p']:
                # 递归处理嵌套标签
                text += extract_code_text(content)
            else:
                text += content.get_text()
        else:
            # 文本节点，保持原始格式
            text += str(content)
    return text

def compress_image(image_data, max_size_kb=500):
    """
    压缩图片数据，使其大小不超过指定的KB数。

    Args:
        image_data (bytes): 原始图片数据。
        max_size_kb (int): 压缩后的最大大小（KB）。

    Returns:
        bytes: 压缩后的图片数据。
    """
    img = Image.open(BytesIO(image_data))
    
    # 如果是PNG格式且有透明通道，转换为RGB
    if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
        bg = Image.new('RGB', img.size, 'white')
        if img.mode == 'P':
            img = img.convert('RGBA')
        bg.paste(img, mask=img.split()[3] if img.mode == 'RGBA' else None)
        img = bg

    # 计算当前大小
    temp_buffer = BytesIO()
    img.save(temp_buffer, format='JPEG', quality=95)
    current_size = len(temp_buffer.getvalue()) / 1024

    # 如果大小已经小于目标大小，直接返回
    if current_size <= max_size_kb:
        temp_buffer.seek(0)
        return temp_buffer.getvalue()

    # 二分法查找合适的压缩质量
    quality_low, quality_high = 5, 95
    while quality_low < quality_high:
        quality = (quality_low + quality_high) // 2
        temp_buffer = BytesIO()
        img.save(temp_buffer, format='JPEG', quality=quality)
        current_size = len(temp_buffer.getvalue()) / 1024

        if abs(current_size - max_size_kb) < 10:  # 如果大小接近目标值，可以接受
            break
        elif current_size > max_size_kb:
            quality_high = quality - 1
        else:
            quality_low = quality + 1

    temp_buffer.seek(0)
    return temp_buffer.getvalue()

def fetch_article_content(url, session):
    """
    获取微信文章的HTML内容。
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36',
    }
    response = session.get(url, headers=headers, timeout=20)
    response.raise_for_status()
    response.encoding = 'utf-8'
    return BeautifulSoup(response.text, 'lxml')

def process_content_elements(doc, content_div, session, filter_ads=True):
    """
    处理文章内容元素并添加到Word文档中。

    Args:
        doc: Word文档对象
        content_div: 文章内容的BeautifulSoup元素
        session: 请求会话对象
        filter_ads: 是否过滤广告内容，默认为True
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36',
    }

    # 统计过滤信息
    filtered_count = 0
    processed_count = 0

    # 扩展搜索范围，包含更多可能的代码标签
    for element in content_div.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'img', 'pre', 'code', 'div']):
        # 检查是否应该停止处理（遇到文章结束标记）
        if should_stop_processing(element):
            logging.info(f"遇到文章结束标记，停止处理后续内容: {element.get_text(strip=True)[:50]}...")
            break

        # 检查是否为广告内容
        if filter_ads and is_advertisement_content(element):
            filtered_count += 1
            logging.debug(f"过滤广告内容: {element.get_text(strip=True)[:50]}...")
            continue

        processed_count += 1
        # 处理代码块（pre标签）
        if element.name == 'pre':
            process_code_block(doc, element)

        # 处理内联代码（code标签，但不在pre内）
        elif element.name == 'code' and not element.find_parent('pre'):
            # 检查是否是独立的代码块
            parent = element.parent
            if parent and parent.name in ['p', 'div'] and len(parent.get_text(strip=True)) == len(element.get_text(strip=True)):
                # 独立代码块
                process_code_block(doc, element)
            else:
                # 内联代码，作为普通段落处理，但保持代码格式
                text = element.get_text()
                if text.strip():
                    para = doc.add_paragraph()
                    run = para.add_run(text)
                    font = run.font
                    font.name = 'Consolas'
                    font.size = Pt(10)
                    rpr = run._element.get_or_add_rPr()
                    rpr.rFonts.set(qn('w:eastAsia'), 'Consolas')

        # 处理可能包含代码的div标签
        elif element.name == 'div':
            # 检查div是否可能是代码容器
            class_attr = element.get('class', [])
            style_attr = element.get('style', '')

            # 常见的代码容器特征
            code_indicators = ['code', 'highlight', 'syntax', 'pre', 'brush']
            is_code_container = any(indicator in ' '.join(class_attr).lower() for indicator in code_indicators)
            is_code_container = is_code_container or 'font-family' in style_attr.lower() and ('mono' in style_attr.lower() or 'consolas' in style_attr.lower())

            if is_code_container:
                process_code_block(doc, element)
            else:
                # 普通div，检查内容
                text = element.get_text(strip=True)
                if text and not element.find_parent(['pre', 'code']):
                    # 避免重复处理已经在其他标签中的内容
                    if not any(child.name in ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'pre', 'code'] for child in element.find_all()):
                        doc.add_paragraph(text)

        # 处理段落
        elif element.name == 'p':
            # 如果p标签在pre或code标签内，则跳过，避免重复处理
            if element.find_parent(['pre', 'code']):
                continue

            # 检查段落是否包含内联代码
            text_parts = []
            for content in element.contents:
                if hasattr(content, 'name') and content.name == 'code':
                    # 内联代码
                    text_parts.append(('code', content.get_text()))
                elif hasattr(content, 'get_text'):
                    text_parts.append(('text', content.get_text()))
                else:
                    text_parts.append(('text', str(content)))

            # 构建段落
            full_text = ''.join(part[1] for part in text_parts).strip()
            if full_text:
                para = doc.add_paragraph()
                for part_type, part_text in text_parts:
                    if part_text.strip():
                        run = para.add_run(part_text)
                        if part_type == 'code':
                            # 内联代码样式
                            font = run.font
                            font.name = 'Consolas'
                            font.size = Pt(10)
                            rpr = run._element.get_or_add_rPr()
                            rpr.rFonts.set(qn('w:eastAsia'), 'Consolas')

        # 处理标题
        elif element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            level = min(int(element.name[1]), 6)  # Word最多支持6级标题
            text = element.get_text(strip=True)
            if text:
                doc.add_heading(text, level=level)

        # 处理图片
        elif element.name == 'img':
            img_url = element.get('data-src') or element.get('src')
            if img_url and img_url.startswith('http'):
                try:
                    img_data = session.get(img_url, headers=headers, timeout=15).content
                    compressed_img_data = compress_image(img_data)
                    image_stream = BytesIO(compressed_img_data)
                    doc.add_picture(image_stream, width=Inches(5.5))
                    image_stream.close()
                    logging.info(f"成功处理图片: {img_url}")
                except requests.exceptions.RequestException as e:
                    logging.error(f"图片下载失败: {img_url}, 错误: {e}")
                except Exception as e:
                    logging.error(f"图片处理失败: {img_url}, 错误: {e}")

    # 输出处理统计信息
    if filter_ads:
        logging.info(f"内容处理完成 - 处理元素: {processed_count}, 过滤广告: {filtered_count}")
    else:
        logging.info(f"内容处理完成 - 处理元素: {processed_count} (广告过滤已禁用)")

def setup_document_styles(doc):
    """
    设置文档样式，包括代码块样式。

    Args:
        doc: Word文档对象
    """
    # 设置正常样式
    normal_style = doc.styles['Normal']
    normal_style.font.name = '宋体'
    normal_style.font.size = Pt(12)
    normal_style._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')

    # 尝试创建或修改代码块样式
    try:
        # 检查是否已存在代码样式
        code_style = None
        for style in doc.styles:
            if style.name == 'Code Block':
                code_style = style
                break

        if not code_style:
            # 创建新的代码块样式
            from docx.enum.style import WD_STYLE_TYPE
            code_style = doc.styles.add_style('Code Block', WD_STYLE_TYPE.PARAGRAPH)

        # 设置代码块样式
        code_font = code_style.font
        code_font.name = 'Consolas'
        code_font.size = Pt(10)
        code_font.color.rgb = RGBColor(0, 0, 0)

        # 设置中文字体
        code_style._element.rPr.rFonts.set(qn('w:eastAsia'), 'Consolas')

        # 设置段落格式
        para_format = code_style.paragraph_format
        para_format.left_indent = Inches(0.2)
        para_format.right_indent = Inches(0.2)
        para_format.space_before = Pt(6)
        para_format.space_after = Pt(6)

    except Exception as e:
        logging.warning(f"设置代码样式失败: {e}")

def create_word_document(soup, session, filter_ads=True):
    """
    创建并填充Word文档。

    Args:
        soup: BeautifulSoup解析的HTML对象
        session: 请求会话对象
        filter_ads: 是否过滤广告内容，默认为True
    """
    title_tag = soup.find('h1', class_=['rich_media_title', 'article_title'])
    title = title_tag.get_text(strip=True) if title_tag else '无标题'
    content_div = soup.find('div', class_=['rich_media_content', 'js_article_content'])
    if not content_div:
        raise ValueError("未找到正文内容")

    doc = Document()

    # 设置文档样式
    setup_document_styles(doc)

    doc.add_heading(title, level=1)
    date_tag = soup.find('em', id='publish_time')
    if date_tag:
        doc.add_paragraph(f"发布日期：{date_tag.get_text(strip=True)}")

    process_content_elements(doc, content_div, session, filter_ads)

    sanitized_title = sanitize_filename(title)
    output_filename = f"{sanitized_title}.docx"
    output_path = os.path.join(OUTPUT_DIR, output_filename)

    doc.save(output_path)
    logging.info(f"文章《{title}》已成功导出至 {output_path}")

    # 输出处理统计信息
    logging.info("功能特性：")
    logging.info("- ✅ 代码格式优化：支持pre、code、div等多种代码标签")
    logging.info("- ✅ 格式保持：保持代码原有缩进和换行格式")
    logging.info("- ✅ 字体优化：使用Consolas等宽字体显示代码")
    logging.info("- ✅ 代码区分：区分内联代码和代码块")
    if filter_ads:
        logging.info("- ✅ 广告过滤：自动识别并过滤广告内容")
    else:
        logging.info("- ⚠️  广告过滤：已禁用")

def weixin_article_to_word(url, filter_ads=True):
    """
    将指定的微信公众号文章URL内容抓取并保存为Word文档。

    Args:
        url: 微信文章URL
        filter_ads: 是否过滤广告内容，默认为True

    Returns:
        bool: 处理成功返回True，失败返回False
    """
    try:
        with requests.Session() as session:
            soup = fetch_article_content(url, session)
            create_word_document(soup, session, filter_ads)
            return True
    except requests.exceptions.RequestException as e:
        logging.error(f"请求失败: {url}, 错误: {e}")
    except Exception as e:
        logging.error(f"处理失败: {url}, 错误: {e}")
    return False

def process_articles():
    """
    批量处理微信文章，将其转换为Word文档。
    用户可以输入多个链接，每行一个，输入空行结束。
    """
    print("=" * 60)
    print("微信公众号文章转Word文档工具")
    print("=" * 60)
    print("功能特性：")
    print("✅ 代码格式优化 - 保持代码缩进和格式")
    print("✅ 智能广告过滤 - 自动识别并过滤广告内容")
    print("✅ 多格式支持 - 支持图片、标题、段落等")
    print("✅ 批量处理 - 支持同时处理多个链接")
    print("-" * 60)

    # 询问是否启用广告过滤
    while True:
        filter_choice = input("是否启用广告过滤功能？(Y/n，默认Y): ").strip().lower()
        if filter_choice in ['', 'y', 'yes', '是']:
            filter_ads = True
            print("✅ 广告过滤功能已启用")
            break
        elif filter_choice in ['n', 'no', '否']:
            filter_ads = False
            print("⚠️  广告过滤功能已禁用")
            break
        else:
            print("请输入 Y 或 N")

    print("\n请输入微信文章链接，每行一个，输入空行结束：")
    urls = []
    while True:
        url = input().strip()
        if not url:
            break
        urls.append(url)

    if not urls:
        print("未输入任何链接")
        return

    print(f"\n开始处理 {len(urls)} 个链接...")
    success_count = 0
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_url = {executor.submit(weixin_article_to_word, url, filter_ads): url for url in urls}
        for i, future in enumerate(as_completed(future_to_url), 1):
            url = future_to_url[future]
            print(f"\n处理第 {i}/{len(urls)} 个链接: {url}")
            try:
                if future.result():
                    success_count += 1
                    print("✅ 处理成功")
                else:
                    print("❌ 处理失败")
            except Exception as exc:
                logging.error(f'{url} 生成时发生错误: {exc}')
                print("❌ 处理异常")

    print("\n" + "=" * 60)
    print(f"处理完成！成功: {success_count}, 失败: {len(urls) - success_count}")
    print(f"文档保存位置: {OUTPUT_DIR}")
    print("=" * 60)

if __name__ == "__main__":
    process_articles()