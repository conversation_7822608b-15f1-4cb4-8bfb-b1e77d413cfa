import os
import asyncio
import aiohttp
import markdownify
from bs4 import BeautifulSoup
import re
import unicodedata
import json
import argparse
import aiofiles
from typing import List, Optional, Dict, Tuple
from pathlib import Path
from pydantic import BaseModel, Field
from rich.console import Console
from rich.progress import Progress

class ContentFilterConfig(BaseModel):
    """内容过滤配置"""
    paragraph_keywords: List[str] = Field(default_factory=list)
    skip_ads: bool = False
    skip_promotions: bool = False

class DownloaderConfig(BaseModel):
    """下载器配置"""
    output_dir: str = "./articles"
    max_workers: int = 10
    timeout: int = 10
    max_retries: int = 3
    retry_delay: int = 2
    filter_config: ContentFilterConfig = Field(default_factory=ContentFilterConfig)

class WeChatArticleDownloader:
    CONFIG_FILE = "wechat_downloader_config.json"

    def __init__(self, config: DownloaderConfig):
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.processed_titles = set()
        self.lock = asyncio.Lock()
        self.console = Console()

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    @staticmethod
    def remove_nonvisible_chars(text: str) -> str:
        """移除不可见字符"""
        return ''.join(c for c in text if unicodedata.category(c) != 'Cn' and c not in (' ', '\n', '\r'))

    def input_urls(self) -> List[Dict]:
        """从文件或输入读取URL"""
        self.console.print("请输入文章URL（每行一个，输入空行结束），或输入文件路径：")
        first_input = input().strip()
        urls = []
        if first_input and os.path.isfile(first_input):
            try:
                with open(first_input, 'r', encoding='utf-8') as f:
                    urls = [{'url': line.strip()} for line in f if line.strip()]
                self.console.print(f"从文件 [cyan]{first_input}[/cyan] 读取了 [bold green]{len(urls)}[/bold green] 个URL")
            except Exception as e:
                self.console.print(f"[bold red]读取文件失败: {e}[/bold red]")
        else:
            if first_input:
                urls.append({'url': first_input})
            while True:
                try:
                    url = input().strip()
                    if not url:
                        break
                    urls.append({'url': url})
                except EOFError:
                    break
        return urls

    def filter_content(self, text: str) -> str:
        """根据配置过滤内容"""
        if self.config.filter_config.paragraph_keywords:
            text = self._filter_paragraphs(text, self.config.filter_config.paragraph_keywords)
        return text

    def _filter_paragraphs(self, text: str, keywords: List[str]) -> str:
        """过滤包含特定关键词的段落"""
        lines = text.split('\n')
        filtered_lines = []
        in_paragraph = False
        current_paragraph = []

        for line in lines:
            if line.strip():
                current_paragraph.append(line)
                in_paragraph = True
            else:
                if in_paragraph:
                    paragraph_text = ' '.join(current_paragraph)
                    if not any(keyword in paragraph_text for keyword in keywords):
                        filtered_lines.extend(current_paragraph)
                    current_paragraph = []
                in_paragraph = False
                filtered_lines.append(line) # Keep empty lines for formatting
        
        if current_paragraph:
            paragraph_text = ' '.join(current_paragraph)
            if not any(keyword in paragraph_text for keyword in keywords):
                filtered_lines.extend(current_paragraph)

        return '\n'.join(filtered_lines)

    def convert_to_markdown(self, content_soup: BeautifulSoup, title: str, create_time: str) -> Tuple[str, str]:
        """将HTML内容转换为Markdown格式"""
        for img in content_soup.find_all('img'):
            img.decompose()

        markdown_content = markdownify.markdownify(str(content_soup))
        markdown_content = '\n'.join([line.strip() for line in markdown_content.split('\n') if line.strip()])
        clean_title = self.remove_nonvisible_chars(title)

        markdown = f'# {clean_title}\n\n{create_time}\n\n{markdown_content}\n'
        markdown = re.sub('\xa0+', '\n', markdown, flags=re.UNICODE)
        markdown = re.sub(r'\]\(http([^)]*)\)', lambda x: '](http' + x.group(1).replace(' ', '%20') + ')', markdown)

        return self.filter_content(markdown), clean_title

    async def get_title_with_retry(self, url: str) -> Optional[Tuple[str, BeautifulSoup]]:
        """带重试机制获取文章标题和Soup对象"""
        for attempt in range(self.config.max_retries):
            try:
                async with self.session.get(url, timeout=self.config.timeout) as response:
                    response.raise_for_status()
                    html = await response.text()
                    soup = BeautifulSoup(html, 'lxml')
                    title_element = soup.find('h1', id="activity-name") or \
                                    soup.find('h2', class_="rich_media_title") or \
                                    soup.find('h1', class_="article-title")
                    if title_element and title_element.text.strip():
                        return title_element.text.strip(), soup
                    self.console.print(f"[yellow]警告: 未能在 {url} 找到标题。[/yellow]")
                    return None, soup # Return soup even if title is not found
            except aiohttp.ClientError as e:
                self.console.print(f"[red]请求错误 ({attempt + 1}/{self.config.max_retries}) for {url}: {e}[/red]")
                if attempt < self.config.max_retries - 1:
                    await asyncio.sleep(self.config.retry_delay * (2 ** attempt))
            except Exception as e:
                self.console.print(f"[bold red]处理URL时发生未知错误 {url}: {e}[/bold red]")
                break
        return None, None

    async def process_url(self, url_data: Dict, progress, task_id) -> bool:
        """处理单个URL"""
        url = url_data['url']
        title, soup = await self.get_title_with_retry(url)
        if not soup:
            progress.update(task_id, advance=1, description=f"[red]失败: {url[:30]}...[/red]")
            return False

        title = title or "Untitled"
        clean_title = self.remove_nonvisible_chars(title)
        filename_base = re.sub(r'[\\/*?"<>|]', '', clean_title)
        filepath = Path(self.config.output_dir) / "MD文档" / f"{filename_base}.md"

        async with self.lock:
            if filename_base in self.processed_titles or filepath.exists():
                self.console.print(f'[yellow]标题 "{filename_base}" 已存在，跳过。[/yellow]')
                progress.update(task_id, advance=1, description=f"[yellow]跳过: {filename_base[:20]}...[/yellow]")
                return False
            self.processed_titles.add(filename_base)

        content_soup = soup.find('div', {'class': 'rich_media_content'})
        if not content_soup:
            progress.update(task_id, advance=1, description=f"[red]无内容: {filename_base[:20]}...[/red]")
            return False

        markdown, _ = self.convert_to_markdown(content_soup, title, "")

        try:
            filepath.parent.mkdir(parents=True, exist_ok=True)
            async with aiofiles.open(filepath, 'w', encoding='utf-8') as f:
                await f.write(markdown)
            progress.update(task_id, advance=1, description=f"[green]成功: {filename_base[:20]}...[/green]")
            return True
        except IOError as e:
            self.console.print(f"[bold red]文件写入失败 {filepath}: {e}[/bold red]")
            progress.update(task_id, advance=1, description=f"[red]写入失败: {filename_base[:20]}...[/red]")
            return False

    async def run(self):
        """运行下载器"""
        urls_data = self.input_urls()
        if not urls_data:
            self.console.print("[yellow]没有输入URL。[/yellow]")
            return

        Path(self.config.output_dir).joinpath("MD文档").mkdir(parents=True, exist_ok=True)
        
        semaphore = asyncio.Semaphore(self.config.max_workers)

        with Progress() as progress:
            task_id = progress.add_task("[cyan]处理中...[/cyan]", total=len(urls_data))

            async def throttled_process_url(url_data):
                async with semaphore:
                    return await self.process_url(url_data, progress, task_id)

            tasks = [throttled_process_url(url_data) for url_data in urls_data]
            results = await asyncio.gather(*tasks)
        
        success_count = sum(1 for r in results if r)
        self.console.print(f"\n[bold green]处理完成: 共{len(urls_data)}条, 成功{success_count}条[/bold green]")

def load_config(config_path: Optional[str]) -> DownloaderConfig:
    """加载配置"""
    if config_path and Path(config_path).exists():
        try:
            return DownloaderConfig.parse_file(config_path)
        except Exception as e:
            print(f"加载配置文件失败，将使用默认配置: {e}")
    return DownloaderConfig()

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='微信公众号文章下载器 (优化版)')
    parser.add_argument('-o', '--output', help=f'输出目录 (默认: {DownloaderConfig().output_dir})')
    parser.add_argument('--config', help='自定义配置文件路径')
    parser.add_argument('--workers', type=int, help=f'并发工作线程数 (默认: {DownloaderConfig().max_workers})')

    args = parser.parse_args()

    config = load_config(args.config)
    if args.output:
        config.output_dir = args.output
    if args.workers:
        config.max_workers = args.workers

    async with WeChatArticleDownloader(config) as downloader:
        await downloader.run()

if __name__ == "__main__":
    asyncio.run(main())