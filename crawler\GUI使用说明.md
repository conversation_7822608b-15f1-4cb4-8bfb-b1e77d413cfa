# GUI 使用说明（精简版）

## 快速开始

```bash
python wechat_spider_gui.py
```

## 必填项

- Cookie：从浏览器开发者工具复制完整值
- Token：在 URL 或请求参数中找到 `token=...`
- 搜索方式：昵称 或 FakeID
- 输出目录：保存 Excel 的路径

## 主要功能

- 图形界面操作、实时日志与进度
- 按昵称或 FakeID 搜索并抓取全部历史文章
- Excel 导出（标题、链接、发布时间）
- 自动保存/加载配置，支持停止爬取

## 常见问题

- Cookie/Token 过期：重新登录公众平台获取最新值
- 未找到公众号：检查昵称是否完全匹配，或改用 FakeID
- Excel 保存失败：确认目录写权限、关闭同名文件

## 注意

- 仅用于学习研究，请遵守平台条款与法律法规
- 配置文件 `wechat_gui_config.json` 含敏感信息，请妥善保管
