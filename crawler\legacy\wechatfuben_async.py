import asyncio
import traceback
import aiohttp
import pandas as pd

"""
日期：2024年11月28日
公众号：哇凉哇哇凉
声明：本文仅供技术研究，请勿用于非法采集，后果自负。
描述: 这是 wechatfuben.py 的异步优化版本，旨在提高爬取速度。
"""


class WeChatSpider:
    """微信公众号爬虫类（异步版本）"""

    def __init__(self, cookie, token):
        """
        初始化爬虫

        :param cookie: 微信公众号平台的Cookie
        :param token: 微信公众号平台的Token
        """
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                          "AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/120.0.0.0 Safari/537.36",
            "Cookie": cookie,
        }
        self.base_params = {
            "lang": "zh_CN",
            "f": "json",
            "token": token,
        }
        self.session = None

    async def __aenter__(self):
        """异步上下文管理器入口，创建 aiohttp.ClientSession"""
        self.session = aiohttp.ClientSession(headers=self.headers)
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口，关闭 aiohttp.ClientSession"""
        if self.session:
            await self.session.close()

    async def get_fakeid(self, nickname, begin=0, count=5):
        """
        根据公众号昵称搜索，异步获取其fakeid

        :param nickname: 公众号昵称
        :param begin: 开始位置
        :param count: 数量
        :return: 公众号的fakeid
        """
        search_url = "https://mp.weixin.qq.com/cgi-bin/searchbiz"
        params = {
            **self.base_params,
            "action": "search_biz",
            "query": nickname,
            "begin": str(begin),
            "count": str(count),
            "ajax": "1",
        }

        try:
            async with self.session.get(search_url, params=params) as response:
                response.raise_for_status()
                data = await response.json()
                if "list" in data and data["list"]:
                    return data["list"][0].get("fakeid")
                return None
        except Exception as e:
            raise Exception(f"获取公众号{nickname}的fakeid失败: {traceback.format_exc()}")

    async def get_articles_page(self, fakeid, begin, count):
        """
        异步获取单页文章

        :param fakeid: 公众号的fakeid
        :param begin: 开始位置
        :param count: 每页数量
        :return: 单页文章列表和总文章数
        """
        art_url = "https://mp.weixin.qq.com/cgi-bin/appmsg"
        params = {
            **self.base_params,
            "query": "",
            "begin": str(begin),
            "count": str(count),
            "type": "9",
            "action": "list_ex",
            "fakeid": fakeid,
        }
        try:
            async with self.session.get(art_url, params=params) as response:
                response.raise_for_status()
                data = await response.json()
                articles = []
                if "app_msg_list" in data:
                    articles = [
                        {
                            "标题": item.get("title"),
                            "链接": item.get("link")
                        }
                        for item in data["app_msg_list"]
                    ]
                return articles, data.get("app_msg_cnt", 0)
        except Exception as e:
            print(f"获取fakeid={fakeid}的文章页面(begin={begin})失败: {e}")
            return [], 0

    async def get_articles(self, fakeid, count=29):
        """
        根据fakeid并发获取所有文章

        :param fakeid: 公众号的fakeid
        :param count: 每页数量
        :return: 所有文章的列表
        """
        # 先获取第一页，以得到总文章数
        initial_articles, total_count = await self.get_articles_page(fakeid, 0, count)
        if total_count == 0:
            return initial_articles

        all_articles = initial_articles
        tasks = []
        for begin in range(count, total_count, count):
            tasks.append(self.get_articles_page(fakeid, begin, count))

        results = await asyncio.gather(*tasks)
        for articles_page, _ in results:
            all_articles.extend(articles_page)

        return all_articles

    async def fetch_articles_by_nickname(self, nickname):
        """
        通过公众号昵称直接异步获取所有文章

        :param nickname: 公众号昵称
        :return: 所有文章的列表
        """
        fakeid = await self.get_fakeid(nickname)
        if not fakeid:
            raise ValueError(f"未找到公众号 {nickname} 的 fakeid")
        return await self.get_articles(fakeid)


async def main():
    """主函数，执行爬虫逻辑"""
    cookie = "RK=q4EkVA94P3; ptcz=2f39=0; _qimeq36=; _qimei_h38=e14777d97cd7ee6ac3036c4102000006118102; eas_sid=01m7L0Z7U3N0Y8u0s4n1x2V2F0; LW_uid=k1h760t7Q380z862i1j2b4c3o4; qq_domain_video_guid_verify=be0e8adaedba6bc0; ua_id=aovHsyXofV8GQ73WAAAAAF1OHXtBu5_g57k3fC36kG0=; wxuin=09049720819582; mm_lang=zh_CN; ts_uid=2829397120; fqm_pvqid=6873888a-f647-49b3-94bd-7b5ca1e10c45; suid=user_1_1479147389; pac_uid=0_xBRhKTZ9mWx25; ptui_loginuin=1479147389; LW_sid=N1J7x3x2Q3u405I4T9H8Z5v8z3; _qimei_fingerprint=ba704b74df0639c6cf78985f43709354; uuid=b23809642eb91de37f35a5e45e7e5599; _clck=dqu2xs|1|fr8|0; rand_info=CAESIIDvgl4ECzFuI/uWIXtlvxczAEp8GrLHYacoHaVeUe1Q; slave_bizuin=3094180885; data_bizuin=3094180885; bizuin=3094180885; data_ticket=XHRoGDl/49F/nlNyI53uSO3iwIKgph2CkSZD9YDwXdBfgeovFbKLkV615FDVUCD3; slave_sid=UVg2b19BUDNvWjBkM0hfMXlubXU0S2lwZWk0N0U4bHg5QTFhdzh3RFdXS0NQSVN2MWJZTlliSjVfNDdycXFvM0JlMURoNTZlN01Qc1djeEpyeHZUUk1EZDVRRmxGQ2gyZFpoYmh2aU5tdE8zcE5RS0lpdnJnYXVmUTZtNFBqeFVkSVJtb09RWEpxMkt6ZVMy; slave_user=gh_783e32eae883; xid=295606d1be7e037ac4f8e27ad4e98de4; rewardsn=; wxtokenkey=777; _clsk=lxhd5g|1732686481063|7|1|mp.weixin.qq.com/weheat-agent/payload/record"
    token = "179888914"  # 需要填入有效的 token
    nickname = "哇凉啊哇凉"

    async with WeChatSpider(cookie, token) as spider:
        try:
            articles = await spider.fetch_articles_by_nickname(nickname)
            if not articles:
                print(f"未获取到公众号 {nickname} 的任何文章。")
                return

            # 将文章列表转换为 DataFrame
            df = pd.DataFrame(articles)

            # 导出到 Excel 文件
            excel_file = fr"C:\Users\<USER>\Desktop\{nickname}_articles_async.xlsx"
            with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, index=False)

            print(f"成功将文章导出到 {excel_file}")
        except Exception as e:
            print(f"发生错误: {e}")


if __name__ == "__main__":
    # 在Windows上运行asyncio事件循环需要特殊的策略
    # if asyncio.get_event_loop().is_running():
    #     asyncio.set_event_loop(asyncio.ProactorEventLoop())
    asyncio.run(main())