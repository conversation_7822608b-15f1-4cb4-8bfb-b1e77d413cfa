from __future__ import annotations

import requests
from requests.adapters import HTTPA<PERSON>pter
from urllib3.util.retry import Retry


DEFAULT_STATUS_FORCELIST = (429, 500, 502, 503, 504)


def build_session_with_retry(user_agent: str | None = None,
                             total: int = 3,
                             backoff_factor: float = 1.0,
                             status_forcelist: tuple[int, ...] = DEFAULT_STATUS_FORCELIST) -> requests.Session:
    """创建带重试策略的 requests.Session。"""
    session = requests.Session()
    retry_strategy = Retry(
        total=total,
        backoff_factor=backoff_factor,
        status_forcelist=list(status_forcelist),
        allowed_methods=["HEAD", "GET", "OPTIONS"]
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    if user_agent:
        session.headers.update({
            'User-Agent': user_agent
        })
    return session


