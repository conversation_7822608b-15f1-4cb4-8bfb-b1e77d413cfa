#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整自动化流程演示
展示从凭证提取到自动爬取的完整流程
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import json
import os
from datetime import datetime

class AutomationDemo:
    def __init__(self, root):
        self.root = root
        self.root.title("完整自动化流程演示")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🚀 完整自动化流程演示", 
                               font=('Microsoft YaHei', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 流程图
        flow_frame = ttk.LabelFrame(main_frame, text="自动化流程", padding="15")
        flow_frame.pack(fill=tk.X, pady=(0, 20))
        
        flow_text = """
🌐 浏览器扩展 → 📡 本地API服务 → 🗄️ 凭证池 → 🕷️ 主爬虫程序
     ↓              ↓              ↓           ↓
  自动提取凭证    接收并验证      自动保存     自动轮换使用
     ↓              ↓              ↓           ↓
  用户一键操作    后台自动处理    智能管理     无人值守爬取
"""
        
        flow_label = ttk.Label(flow_frame, text=flow_text, font=('Consolas', 10))
        flow_label.pack()
        
        # 步骤控制
        steps_frame = ttk.LabelFrame(main_frame, text="演示步骤", padding="15")
        steps_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 步骤1
        step1_frame = ttk.Frame(steps_frame)
        step1_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(step1_frame, text="步骤1: 启动浏览器扩展和本地服务", 
                 font=('Microsoft YaHei', 10, 'bold')).pack(anchor=tk.W)
        
        step1_buttons = ttk.Frame(step1_frame)
        step1_buttons.pack(fill=tk.X, pady=5)
        
        ttk.Button(step1_buttons, text="生成浏览器扩展", 
                  command=self.generate_extension).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(step1_buttons, text="启动本地服务", 
                  command=self.start_local_service).pack(side=tk.LEFT, padx=(0, 10))
        
        # 步骤2
        step2_frame = ttk.Frame(steps_frame)
        step2_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(step2_frame, text="步骤2: 使用浏览器扩展提取凭证", 
                 font=('Microsoft YaHei', 10, 'bold')).pack(anchor=tk.W)
        ttk.Label(step2_frame, text="在Chrome中安装扩展，访问微信公众平台，点击扩展图标提取凭证", 
                 font=('Microsoft YaHei', 9), foreground='gray').pack(anchor=tk.W, padx=(20, 0))
        
        # 步骤3
        step3_frame = ttk.Frame(steps_frame)
        step3_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(step3_frame, text="步骤3: 查看凭证池状态", 
                 font=('Microsoft YaHei', 10, 'bold')).pack(anchor=tk.W)
        
        step3_buttons = ttk.Frame(step3_frame)
        step3_buttons.pack(fill=tk.X, pady=5)
        
        ttk.Button(step3_buttons, text="查看凭证池", 
                  command=self.show_credential_pool).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(step3_buttons, text="验证所有凭证", 
                  command=self.validate_all_credentials).pack(side=tk.LEFT, padx=(0, 10))
        
        # 步骤4
        step4_frame = ttk.Frame(steps_frame)
        step4_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(step4_frame, text="步骤4: 启动自动化爬虫", 
                 font=('Microsoft YaHei', 10, 'bold')).pack(anchor=tk.W)
        
        step4_buttons = ttk.Frame(step4_frame)
        step4_buttons.pack(fill=tk.X, pady=5)
        
        ttk.Button(step4_buttons, text="启动主爬虫", 
                  command=self.launch_main_spider).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(step4_buttons, text="模拟自动爬取", 
                  command=self.simulate_auto_crawling).pack(side=tk.LEFT, padx=(0, 10))
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="系统状态", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.status_var = tk.StringVar(value="系统就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                                font=('Microsoft YaHei', 10))
        status_label.pack()
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="演示日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def log_message(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def generate_extension(self):
        """生成浏览器扩展"""
        self.log_message("🧩 启动浏览器扩展助手...")
        self.status_var.set("正在生成浏览器扩展...")
        
        try:
            import subprocess
            import sys
            from common.path_utils import resolve_path_from_dir
            target = resolve_path_from_dir("extensions", "browser_extension_helper.py")
            subprocess.Popen([sys.executable, target])
            self.log_message("✅ 浏览器扩展助手已启动")
            self.log_message("📝 请在扩展助手中生成并安装Chrome扩展")
        except Exception as e:
            self.log_message(f"❌ 启动扩展助手失败: {str(e)}")
            
    def start_local_service(self):
        """启动本地服务"""
        self.log_message("📡 启动本地API服务...")
        self.status_var.set("本地服务运行中...")
        
        def service_thread():
            try:
                import http.server
                import socketserver
                import json
                from urllib.parse import urlparse
                
                class CredentialHandler(http.server.BaseHTTPRequestHandler):
                    def do_POST(self):
                        if self.path == '/credentials':
                            content_length = int(self.headers['Content-Length'])
                            post_data = self.rfile.read(content_length)
                            
                            try:
                                credentials = json.loads(post_data.decode('utf-8'))
                                
                                # 自动保存到凭证池
                                self.save_to_pool(credentials)
                                
                                self.send_response(200)
                                self.send_header('Content-type', 'application/json')
                                self.send_header('Access-Control-Allow-Origin', '*')
                                self.end_headers()
                                self.wfile.write(b'{"status": "success"}')
                                
                            except Exception as e:
                                self.send_response(400)
                                self.end_headers()
                                
                        else:
                            self.send_response(404)
                            self.end_headers()
                            
                    def do_OPTIONS(self):
                        self.send_response(200)
                        self.send_header('Access-Control-Allow-Origin', '*')
                        self.send_header('Access-Control-Allow-Methods', 'POST, OPTIONS')
                        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                        self.end_headers()
                        
                    def save_to_pool(self, credentials):
                        from smart_credential_manager import credential_manager
                        result = credential_manager.add_credential(
                            credentials['cookie'], 
                            credentials['token'],
                            source='browser_extension'
                        )
                        print(f"凭证已保存: {result}")
                        
                    def log_message(self, format, *args):
                        pass  # 禁用默认日志
                
                with socketserver.TCPServer(("", 8888), CredentialHandler) as httpd:
                    httpd.serve_forever()
                    
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"❌ 本地服务启动失败: {str(e)}"))
        
        thread = threading.Thread(target=service_thread, daemon=True)
        thread.start()
        self.log_message("✅ 本地API服务已启动在端口8888")
        self.log_message("🔗 浏览器扩展可以自动发送凭证到此服务")
        
    def show_credential_pool(self):
        """显示凭证池状态"""
        self.log_message("🗄️ 检查凭证池状态...")
        
        try:
            from smart_credential_manager import credential_manager
            status = credential_manager.get_pool_status()
            
            status_text = f"""凭证池状态报告:

📊 统计信息:
• 总凭证数: {status['total']}
• 有效凭证: {status['valid']}
• 无效凭证: {status['invalid']}
• 自动提取: {status['auto_extracted']}
• 手动添加: {status['manual']}

🔄 当前状态:
• 当前使用: {status['current'] or '无'}"""

            self.log_message("✅ 凭证池状态获取成功")
            messagebox.showinfo("凭证池状态", status_text)
            
        except Exception as e:
            self.log_message(f"❌ 获取凭证池状态失败: {str(e)}")
            
    def validate_all_credentials(self):
        """验证所有凭证"""
        self.log_message("🔍 开始验证所有凭证...")
        self.status_var.set("正在验证凭证...")
        
        def validate_thread():
            try:
                from smart_credential_manager import credential_manager
                results = credential_manager.validate_all_credentials()
                
                valid_count = len([r for r in results if r['valid']])
                total_count = len(results)
                
                self.root.after(0, lambda: self.log_message(f"✅ 验证完成: {valid_count}/{total_count} 个凭证有效"))
                self.root.after(0, lambda: self.status_var.set(f"验证完成: {valid_count}/{total_count} 有效"))
                
                # 显示详细结果
                result_text = "凭证验证结果:\n\n"
                for result in results:
                    status = "✅" if result['valid'] else "❌"
                    result_text += f"{status} {result['name']}: {result['message']}\n"
                    
                self.root.after(0, lambda: messagebox.showinfo("验证结果", result_text))
                
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"❌ 验证失败: {str(e)}"))
                
        thread = threading.Thread(target=validate_thread, daemon=True)
        thread.start()
        
    def launch_main_spider(self):
        """启动主爬虫"""
        self.log_message("🕷️ 启动主爬虫程序...")
        
        try:
            import subprocess
            import sys
            from common.path_utils import resolve_path_from_apps
            target = resolve_path_from_apps("wechat_spider_gui.py")
            subprocess.Popen([sys.executable, target])
            self.log_message("✅ 主爬虫程序已启动")
            self.log_message("💡 请在主程序中启用'自动使用凭证池'选项")
        except Exception as e:
            self.log_message(f"❌ 启动主爬虫失败: {str(e)}")
            
    def simulate_auto_crawling(self):
        """模拟自动爬取过程"""
        self.log_message("🎭 开始模拟自动爬取过程...")
        self.status_var.set("模拟爬取中...")
        
        def simulate_thread():
            try:
                from smart_credential_manager import credential_manager
                
                # 模拟获取凭证
                self.root.after(0, lambda: self.log_message("🔄 从凭证池获取凭证..."))
                time.sleep(1)
                
                cred = credential_manager.get_next_credential()
                if cred:
                    self.root.after(0, lambda: self.log_message(f"✅ 获取到凭证: {cred.get('name', '未命名')}"))
                else:
                    self.root.after(0, lambda: self.log_message("❌ 凭证池为空"))
                    return
                
                # 模拟验证凭证
                self.root.after(0, lambda: self.log_message("🔍 验证凭证有效性..."))
                time.sleep(1)
                
                is_valid, message = credential_manager.validate_credential(cred)
                if is_valid:
                    self.root.after(0, lambda: self.log_message("✅ 凭证验证成功"))
                else:
                    self.root.after(0, lambda: self.log_message(f"❌ 凭证验证失败: {message}"))
                    self.root.after(0, lambda: self.log_message("🔄 尝试切换到下一个凭证..."))
                    
                    next_cred = credential_manager.get_next_credential()
                    if next_cred:
                        self.root.after(0, lambda: self.log_message(f"✅ 切换到凭证: {next_cred.get('name', '未命名')}"))
                    else:
                        self.root.after(0, lambda: self.log_message("❌ 没有更多可用凭证"))
                        return
                
                # 模拟爬取过程
                self.root.after(0, lambda: self.log_message("🕷️ 开始爬取文章..."))
                for i in range(5):
                    time.sleep(0.5)
                    self.root.after(0, lambda i=i: self.log_message(f"📄 已爬取 {(i+1)*10} 篇文章"))
                
                self.root.after(0, lambda: self.log_message("💾 自动保存中间结果..."))
                time.sleep(0.5)
                
                self.root.after(0, lambda: self.log_message("✅ 模拟爬取完成！"))
                self.root.after(0, lambda: self.status_var.set("模拟完成"))
                
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"❌ 模拟过程出错: {str(e)}"))
                
        thread = threading.Thread(target=simulate_thread, daemon=True)
        thread.start()

def main():
    """主函数"""
    root = tk.Tk()
    app = AutomationDemo(root)
    root.mainloop()

if __name__ == "__main__":
    main()
