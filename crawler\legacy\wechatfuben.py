import traceback
import requests
import pandas as pd
import time

"""
日期：2024年11月28日
公众号：哇凉哇哇凉
声明：本文仅供技术研究，请勿用于非法采集，后果自负。
"""


class WeChatSpider:
    """微信公众号爬虫类"""
    def __init__(self, cookie, token):
        """
        初始化爬虫

        :param cookie: 微信公众号平台的Cookie
        :param token: 微信公众号平台的Token
        """
        self.session = requests.Session()
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                          "AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/120.0.0.0 Safari/537.36",
            "Cookie": cookie,
        }
        self.base_params = {
            "lang": "zh_CN",
            "f": "json",
            "token": token,
        }

    def get_fakeid(self, nickname, begin=0, count=5):
        """
        根据公众号昵称搜索，获取其fakeid

        :param nickname: 公众号昵称
        :param begin: 开始位置
        :param count: 数量
        :return: 公众号的fakeid
        """
        """获取公众号的 FakeID"""
        search_url = "https://mp.weixin.qq.com/cgi-bin/searchbiz"
        params = {
            **self.base_params,
            "action": "search_biz",
            "query": nickname,
            "begin": begin,
            "count": count,
            "ajax": "1",
        }

        try:
            response = self.session.get(search_url, headers=self.headers, params=params)
            response.raise_for_status()
            data = response.json()
            if "list" in data and data["list"]:
                for item in data["list"]:
                    if item.get("nickname") == nickname:
                        return item.get("fakeid")
            return None
        except Exception as e:
            raise Exception(f"获取公众号{nickname}的fakeid失败: {traceback.format_exc()}")

    def get_articles(self, fakeid, begin=0, count=5):
        """
        根据fakeid获取公众号的文章列表，并自动翻页

        :param fakeid: 公众号的fakeid
        :param begin: 开始位置
        :param count: 每页数量
        :return: 所有文章的列表
        """
        """获取公众号的文章列表并翻页"""
        all_articles = []
        while True:
            art_url = "https://mp.weixin.qq.com/cgi-bin/appmsg"
            params = {
                **self.base_params,
                "query": "",
                "begin": begin,
                "count": count,
                "type": 9,
                "action": "list_ex",
                "fakeid": fakeid,
            }
            print(f"正在获取文章，起始位置: {begin}...")
            try:
                response = self.session.get(art_url, headers=self.headers, params=params)
                response.raise_for_status()
                data = response.json()
                # 增加对返回数据的详细判断
                if "app_msg_list" in data and data["app_msg_list"]:
                    articles_on_page = data["app_msg_list"]
                    print(f"找到 {len(articles_on_page)} 篇文章。")
                    articles = [
                        {
                            "标题": item.get("title"),
                            "链接": item.get("link")
                        }
                        for item in articles_on_page
                    ]
                    all_articles.extend(articles)
                    print(f"总共已收集 {len(all_articles)} 篇文章。")

                    # 判断是否有下一页
                    if len(articles_on_page) < count:
                        print("已到达最后一页，抓取结束。")
                        break
                    else:
                        begin += count
                        print("准备获取下一页...")
                        time.sleep(2)  # 增加延时，防止请求过于频繁
                else:
                    # 检查是否因为凭证过期导致失败
                    if 'base_resp' in data and data['base_resp'].get('ret') == 200013:
                        print("错误：Cookie/Token 已过期或无效，请重新登录微信公众平台获取。")
                    else:
                        print(f"未能获取到文章列表，API返回数据: {data}")
                    break
            except Exception as e:
                raise Exception(f"获取fakeid={fakeid}的文章失败: {traceback.format_exc()}")

        return all_articles

    def fetch_articles_by_nickname(self, nickname, begin=0, count=5):
        """
        通过公众号昵称直接获取所有文章

        :param nickname: 公众号昵称
        :param begin: 开始位置
        :param count: 数量
        :return: 所有文章的列表
        """
        """通过昵称直接获取文章"""
        fakeid = self.get_fakeid(nickname, begin, count)
        if not fakeid:
            raise ValueError(f"未找到公众号 {nickname} 的 fakeid")
        return self.get_articles(fakeid)


def main():
    """主函数，执行爬虫逻辑"""
    cookie = "pac_uid=0_dBEpKBPeH4yFp; _qimei_uuid42=1920a140c35100f006225434d31cb7f38362bd3664; _qimei_h38=8f95c30406225434d31cb7f30200000a81920a; pgv_pvid=2747312680; RK=xANhcgG+9l; ptcz=583604042df64ce3d9a62dbae3a5427294c6bd0e4a626f107d2a5290e40989ab; wxuin=39547569653086; mm_lang=zh_CN; suid=user_0_dBEpKBPeH4yFp; _qimei_q32=b2865cc032c3a2e35ad2b18f05234a98; _qimei_q36=c5682cd1899a830172e3de0a30001151920a; _qimei_fingerprint=2f4642bf0769087d5b31b1a307301a9f; ts_uid=8124239387; xid=540ba110a341dbdc208d5576bbd6ae12; rand_info=CAESIHv9qQygLidFUU0xxw5rnwf8jsDvvs0CebwIHr2rRDpz; slave_user=gh_561fbcf46439; slave_sid=SG9IWEhEQ1lDMnNBOVdfTjdJeE9PT0R4WVl1UlNWbmI1bFNhdWNoT2J4VDNvOFlDUnZNTDg5UldyS0hUdzg3UmY4QllrM0F5TmJtRVFIQ2xmRVE5ekY2TnROOEpnQmkzNThfRkZISHM2WFZIclJLanJCcU5DSjcxWDAyWG92SGVFeWR1M0lGN2Znd01mNmc5; slave_bizuin=3551612504; data_bizuin=3551612504; ua_id=8xpv0TytEquGINmZAAAAAIgWITw4yr6EGE4KLXceP44=; bizuin=3551612504; data_ticket=6e825Jdu0W1bWlpQFfBOQmSrXRavru5nibm4NEAJ5+uSbvDObPPuJH77QCGUOYhs; _clck=3551612504|1|fxs|0; rewardsn=; wxtokenkey=777; _clsk=1swz4y5|1753118957405|19|1|mp.weixin.qq.com/weheat-agent/payload/record"
    token = "550864750"  # 需要填入有效的 token
    nickname = "Breath深呼吸"

    spider = WeChatSpider(cookie, token)

    try:
        articles = spider.fetch_articles_by_nickname(nickname)
        if not articles:
            print(f"未获取到公众号 {nickname} 的任何文章。")
            return

        # 将文章列表转换为 DataFrame
        df = pd.DataFrame(articles)

        # 导出到 Excel 文件
        excel_file = fr"C:\Users\<USER>\Desktop\{nickname}_articles.xlsx"
        # pandas 2.x to_excel has no encoding parameter, use ExcelWriter
        with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
            df.to_excel(writer, index=False)

        print(f"成功将文章导出到 {excel_file}")
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    main()