#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
自动凭证获取器
使用浏览器自动化安全获取<PERSON>ie和Token
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import json
import os
from datetime import datetime
import subprocess
import sys

class AutoCredentialFetcher:
    def __init__(self, root):
        self.root = root
        self.root.title("自动凭证获取器")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        
        self.is_fetching = False
        self.browser_process = None
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🤖 自动凭证获取器", 
                               font=('Microsoft YaHei', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 方法选择
        method_frame = ttk.LabelFrame(main_frame, text="获取方法", padding="10")
        method_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.method_var = tk.StringVar(value="browser_auto")
        
        ttk.Radiobutton(method_frame, text="🌐 浏览器自动化（推荐）", 
                       variable=self.method_var, value="browser_auto").pack(anchor=tk.W, pady=2)
        ttk.Label(method_frame, text="    自动打开浏览器，引导用户登录后自动提取凭证", 
                 font=('Microsoft YaHei', 9), foreground='gray').pack(anchor=tk.W, padx=(20, 0))
        
        ttk.Radiobutton(method_frame, text="📋 剪贴板监控", 
                       variable=self.method_var, value="clipboard").pack(anchor=tk.W, pady=2)
        ttk.Label(method_frame, text="    监控剪贴板，自动识别复制的Cookie和Token", 
                 font=('Microsoft YaHei', 9), foreground='gray').pack(anchor=tk.W, padx=(20, 0))
        
        ttk.Radiobutton(method_frame, text="🔍 网络监听", 
                       variable=self.method_var, value="network").pack(anchor=tk.W, pady=2)
        ttk.Label(method_frame, text="    监听本地网络请求，自动捕获凭证信息", 
                 font=('Microsoft YaHei', 9), foreground='gray').pack(anchor=tk.W, padx=(20, 0))
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.start_button = ttk.Button(button_frame, text="开始自动获取", 
                                      command=self.start_auto_fetch)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止获取", 
                                     command=self.stop_auto_fetch, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="安装依赖", 
                  command=self.install_dependencies).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="使用说明", 
                  command=self.show_instructions).pack(side=tk.LEFT)
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="获取状态", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.status_var = tk.StringVar(value="就绪 - 请选择获取方法")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack()
        
        # 结果显示
        result_frame = ttk.LabelFrame(main_frame, text="获取结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # Cookie显示
        ttk.Label(result_frame, text="Cookie:").pack(anchor=tk.W)
        self.cookie_text = tk.Text(result_frame, height=4, width=70)
        self.cookie_text.pack(fill=tk.X, pady=(0, 10))
        
        # Token显示
        ttk.Label(result_frame, text="Token:").pack(anchor=tk.W)
        self.token_var = tk.StringVar()
        token_entry = ttk.Entry(result_frame, textvariable=self.token_var, width=70)
        token_entry.pack(fill=tk.X, pady=(0, 10))
        
        # 操作按钮
        action_frame = ttk.Frame(result_frame)
        action_frame.pack(fill=tk.X)
        
        ttk.Button(action_frame, text="验证凭证", 
                  command=self.validate_credentials).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(action_frame, text="保存到凭证池", 
                  command=self.save_to_pool).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(action_frame, text="复制到剪贴板", 
                  command=self.copy_to_clipboard).pack(side=tk.LEFT)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def log_message(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def start_auto_fetch(self):
        """开始自动获取"""
        method = self.method_var.get()
        
        if method == "browser_auto":
            self.start_browser_automation()
        elif method == "clipboard":
            self.start_clipboard_monitoring()
        elif method == "network":
            self.start_network_monitoring()
            
    def start_browser_automation(self):
        """启动浏览器自动化"""
        self.log_message("启动浏览器自动化模式...")
        
        try:
            # 检查selenium是否安装
            import selenium
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            
            self.is_fetching = True
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.status_var.set("🌐 浏览器自动化运行中...")
            
            # 在新线程中运行
            threading.Thread(target=self.browser_automation_worker, daemon=True).start()
            
        except ImportError:
            messagebox.showerror("依赖缺失", 
                               "需要安装selenium库才能使用浏览器自动化功能\n\n点击'安装依赖'按钮自动安装")
            
    def browser_automation_worker(self):
        """浏览器自动化工作线程"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.common.by import By
            import time
            
            self.log_message("正在启动Chrome浏览器...")
            
            # 配置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 启动浏览器
            driver = webdriver.Chrome(options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.log_message("浏览器已启动，正在打开微信公众平台...")
            
            # 打开微信公众平台
            driver.get("https://mp.weixin.qq.com/")
            
            self.log_message("请在浏览器中完成登录...")
            self.log_message("登录成功后，程序将自动提取Cookie和Token")
            
            # 等待用户登录
            login_success = False
            check_count = 0
            
            while self.is_fetching and check_count < 300:  # 最多等待5分钟
                try:
                    # 检查是否登录成功（查找特定元素）
                    if "token=" in driver.current_url:
                        login_success = True
                        break
                        
                    time.sleep(1)
                    check_count += 1
                    
                    if check_count % 30 == 0:  # 每30秒提示一次
                        self.log_message(f"等待登录中... ({check_count//30}/10)")
                        
                except Exception as e:
                    self.log_message(f"检查登录状态时出错: {str(e)}")
                    break
            
            if login_success:
                self.log_message("✅ 检测到登录成功，正在提取凭证...")
                
                # 提取Cookie
                cookies = driver.get_cookies()
                cookie_str = "; ".join([f"{cookie['name']}={cookie['value']}" for cookie in cookies])
                
                # 提取Token
                current_url = driver.current_url
                token = ""
                if "token=" in current_url:
                    token_start = current_url.find("token=") + 6
                    token_end = current_url.find("&", token_start)
                    if token_end == -1:
                        token = current_url[token_start:]
                    else:
                        token = current_url[token_start:token_end]
                
                # 更新界面
                self.root.after(0, lambda: self.cookie_text.delete("1.0", tk.END))
                self.root.after(0, lambda: self.cookie_text.insert("1.0", cookie_str))
                self.root.after(0, lambda: self.token_var.set(token))
                
                self.log_message("✅ 凭证提取成功！")
                self.log_message("请验证凭证有效性后保存使用")
                
            else:
                self.log_message("❌ 登录超时或被用户取消")
                
            # 关闭浏览器
            driver.quit()
            
        except Exception as e:
            self.log_message(f"浏览器自动化过程中发生错误: {str(e)}")
            
        finally:
            self.root.after(0, self.reset_buttons)
            
    def start_clipboard_monitoring(self):
        """启动剪贴板监控"""
        self.log_message("启动剪贴板监控模式...")
        
        self.is_fetching = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_var.set("📋 剪贴板监控运行中...")
        
        threading.Thread(target=self.clipboard_monitoring_worker, daemon=True).start()
        
    def clipboard_monitoring_worker(self):
        """剪贴板监控工作线程"""
        try:
            import pyperclip
            
            self.log_message("剪贴板监控已启动")
            self.log_message("请复制包含Cookie或Token的文本...")
            
            last_clipboard = ""
            
            while self.is_fetching:
                try:
                    current_clipboard = pyperclip.paste()
                    
                    if current_clipboard != last_clipboard and current_clipboard:
                        last_clipboard = current_clipboard
                        
                        # 检查是否包含Cookie
                        if self.is_cookie_format(current_clipboard):
                            self.log_message("🍪 检测到Cookie格式文本")
                            self.root.after(0, lambda: self.cookie_text.delete("1.0", tk.END))
                            self.root.after(0, lambda: self.cookie_text.insert("1.0", current_clipboard))
                            
                        # 检查是否包含Token
                        elif self.is_token_format(current_clipboard):
                            self.log_message("🔑 检测到Token格式文本")
                            self.root.after(0, lambda: self.token_var.set(current_clipboard))
                            
                    time.sleep(0.5)
                    
                except Exception as e:
                    self.log_message(f"剪贴板监控错误: {str(e)}")
                    time.sleep(1)
                    
        except ImportError:
            self.log_message("需要安装pyperclip库才能使用剪贴板监控功能")
            messagebox.showerror("依赖缺失", "需要安装pyperclip库\n\n点击'安装依赖'按钮自动安装")
            
        finally:
            self.root.after(0, self.reset_buttons)
            
    def start_network_monitoring(self):
        """启动网络监听"""
        self.log_message("网络监听功能开发中...")
        messagebox.showinfo("功能开发中", "网络监听功能正在开发中\n\n请使用浏览器自动化或剪贴板监控功能")
        
    def is_cookie_format(self, text):
        """检查是否为Cookie格式"""
        return ("=" in text and 
                (";" in text or len(text) > 100) and
                any(keyword in text.lower() for keyword in ['session', 'token', 'auth', 'login']))
                
    def is_token_format(self, text):
        """检查是否为Token格式"""
        return (text.isdigit() and len(text) > 8) or \
               (len(text) > 20 and text.replace('_', '').replace('-', '').isalnum())
               
    def stop_auto_fetch(self):
        """停止自动获取"""
        self.is_fetching = False
        self.log_message("正在停止自动获取...")
        self.reset_buttons()
        
    def reset_buttons(self):
        """重置按钮状态"""
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_var.set("已停止")
        
    def validate_credentials(self):
        """验证凭证"""
        cookie = self.cookie_text.get("1.0", tk.END).strip()
        token = self.token_var.get().strip()
        
        if not cookie or not token:
            messagebox.showwarning("提示", "请先获取Cookie和Token")
            return
            
        def validate_thread():
            try:
                from wechat_spider_gui import WeChatSpider
                spider = WeChatSpider(cookie, token, progress_callback=self.log_message)
                is_valid, message = spider.test_credentials()
                
                if is_valid:
                    self.root.after(0, lambda: messagebox.showinfo("验证成功", "凭证有效！可以保存使用"))
                else:
                    self.root.after(0, lambda: messagebox.showerror("验证失败", f"凭证无效: {message}"))
                    
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("验证异常", f"验证过程中发生异常: {str(e)}"))
                
        threading.Thread(target=validate_thread, daemon=True).start()
        
    def save_to_pool(self):
        """保存到凭证池"""
        cookie = self.cookie_text.get("1.0", tk.END).strip()
        token = self.token_var.get().strip()
        
        if not cookie or not token:
            messagebox.showwarning("提示", "请先获取Cookie和Token")
            return
            
        try:
            # 启动凭证池管理器
            from common.path_utils import resolve_path_from_dir
            target = resolve_path_from_dir("credentials", "credential_pool.py")
            subprocess.Popen([sys.executable, target])
            messagebox.showinfo("提示", "凭证池管理器已启动\n\n请在凭证池中添加这些凭证")
        except Exception as e:
            messagebox.showerror("启动失败", f"无法启动凭证池管理器: {str(e)}")
            
    def copy_to_clipboard(self):
        """复制到剪贴板"""
        cookie = self.cookie_text.get("1.0", tk.END).strip()
        token = self.token_var.get().strip()
        
        if not cookie or not token:
            messagebox.showwarning("提示", "请先获取Cookie和Token")
            return
            
        credentials_text = f"Cookie: {cookie}\nToken: {token}"
        self.root.clipboard_clear()
        self.root.clipboard_append(credentials_text)
        messagebox.showinfo("复制成功", "凭证已复制到剪贴板")
        
    def install_dependencies(self):
        """安装依赖"""
        self.log_message("正在安装依赖包...")
        
        def install_thread():
            try:
                # 安装selenium
                subprocess.run([sys.executable, "-m", "pip", "install", "selenium"], 
                             capture_output=True, text=True, check=True)
                self.log_message("✅ selenium 安装成功")
                
                # 安装pyperclip
                subprocess.run([sys.executable, "-m", "pip", "install", "pyperclip"], 
                             capture_output=True, text=True, check=True)
                self.log_message("✅ pyperclip 安装成功")
                
                # 提示安装ChromeDriver
                self.log_message("⚠️ 请确保已安装ChromeDriver")
                self.log_message("下载地址: https://chromedriver.chromium.org/")
                
                self.root.after(0, lambda: messagebox.showinfo("安装完成", 
                    "依赖包安装完成！\n\n请确保已安装ChromeDriver\n下载地址: https://chromedriver.chromium.org/"))
                
            except Exception as e:
                self.log_message(f"❌ 安装失败: {str(e)}")
                self.root.after(0, lambda: messagebox.showerror("安装失败", f"依赖安装失败: {str(e)}"))
                
        threading.Thread(target=install_thread, daemon=True).start()
        
    def show_instructions(self):
        """显示使用说明"""
        instructions = """自动凭证获取器使用说明

🌐 浏览器自动化模式（推荐）：
1. 点击"开始自动获取"
2. 程序会自动打开Chrome浏览器
3. 在浏览器中登录微信公众平台
4. 登录成功后程序自动提取Cookie和Token
5. 验证凭证有效性后保存使用

📋 剪贴板监控模式：
1. 点击"开始自动获取"
2. 手动登录微信公众平台
3. 按F12打开开发者工具
4. 复制Cookie或Token到剪贴板
5. 程序自动识别并填充

🔧 依赖要求：
- selenium（浏览器自动化）
- pyperclip（剪贴板监控）
- ChromeDriver（Chrome浏览器驱动）

⚠️ 安全提醒：
- 所有操作在本地进行，不会上传任何数据
- 建议在安全的网络环境中使用
- 获取的凭证请妥善保管"""

        messagebox.showinfo("使用说明", instructions)

def main():
    """主函数"""
    root = tk.Tk()
    app = AutoCredentialFetcher(root)
    root.mainloop()

if __name__ == "__main__":
    main()
