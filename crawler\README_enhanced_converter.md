# 增强型 Markdown 转换器（精简版说明）

## 快速开始（以 apps/ 为工作目录）

- 图形界面（推荐）：

```bash
cd apps
python enhanced_markdown_converter.py --gui
```

- 命令行：

```bash
cd apps
python enhanced_markdown_converter.py --file path/to/file.pdf --output ./output
python enhanced_markdown_converter.py --url https://example.com/article --output ./output
```

## 安装

```bash
pip install markdownify beautifulsoup4 lxml requests
# 可选：启用多格式文件转换
pip install markitdown[all]
```

## 主要能力

- 网页与微信公众号文章转换（自动提取标题与正文）
- 多种文件格式到 Markdown（依赖 MarkItDown）
- GUI 批量处理、并发与进度展示
- 关键词过滤与去重（同名文件自动跳过）

## 配置

首次运行会生成 `enhanced_converter_config.json`：

```json
{
  "paragraph_keywords": ["广告", "推广"],
  "image_hashes": [],
  "skip_ads": false,
  "skip_promotions": false
}
```

## 输出目录

所有结果写入 `输出目录/MD文档/` 下（存在同名时自动跳过）。

## 常见问题

- 未安装 MarkItDown：仅支持基础网页/少量文件；安装 `markitdown[all]` 可扩展能力。
- 转换失败/网络错误：程序会自动重试，不可达时跳过并继续。
- 重复文件：若文件名已存在或本次已处理过将自动跳过。

## 免责声明

仅供学习研究使用，请遵守目标网站使用条款。
