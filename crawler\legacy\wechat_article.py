import traceback
import requests
import pandas as pd

"""
日期：2024年11月28日
公众号：哇凉哇哇凉
声明：本文仅供技术研究，请勿用于非法采集，后果自负。
"""


class WeChatSpider:
    def __init__(self, cookie, token):
        self.session = requests.Session()
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                          "AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/120.0.0.0 Safari/537.36",
            "Cookie": cookie,
        }
        self.base_params = {
            "lang": "zh_CN",
            "f": "json",
            "token": token,
        }

    def get_fakeid(self, nickname, begin=0, count=5):
        """获取公众号的 FakeID"""
        search_url = "https://mp.weixin.qq.com/cgi-bin/searchbiz"
        params = {
            **self.base_params,
            "action": "search_biz",
            "query": nickname,
            "begin": begin,
            "count": count,
            "ajax": "1",
        }

        try:
            response = self.session.get(search_url, headers=self.headers, params=params)
            response.raise_for_status()
            data = response.json()
            if "list" in data and data["list"]:
                return data["list"][0].get("fakeid")
            return None
        except Exception as e:
            raise Exception(f"获取公众号{nickname}的fakeid失败: {traceback.format_exc()}")

    def get_articles(self, fakeid, begin=0, count=29):
        """获取公众号的文章列表并翻页"""
        all_articles = []
        while True:
            art_url = "https://mp.weixin.qq.com/cgi-bin/appmsg"
            params = {
                **self.base_params,
                "query": "",
                "begin": begin,
                "count": count,
                "type": 9,
                "action": "list_ex",
                "fakeid": fakeid,
            }

            try:
                response = self.session.get(art_url, headers=self.headers, params=params)
                response.raise_for_status()
                data = response.json()
                if "app_msg_list" in data:
                    articles = [
                        {
                            "标题": item.get("title"),
                            "链接": item.get("link")
                        }
                        for item in data["app_msg_list"]
                    ]
                    all_articles.extend(articles)

                    # 判断是否有下一页
                    if len(data["app_msg_list"]) < count:
                        break  # 如果当前页的文章数少于请求的数量，表示已获取所有文章
                    else:
                        begin += count  # 否则，翻到下一页，继续获取
                else:
                    break
            except Exception as e:
                raise Exception(f"获取fakeid={fakeid}的文章失败: {traceback.format_exc()}")

        return all_articles

    def fetch_articles_by_nickname(self, nickname, begin=0, count=5):
        """通过昵称直接获取文章"""
        fakeid = self.get_fakeid(nickname, begin, count)
        if not fakeid:
            raise ValueError(f"未找到公众号 {nickname} 的 fakeid")
        return self.get_articles(fakeid, begin, count)


def main():
    cookie = "appmsglist_action_3551612504=card; ua_id=qH8hPvV0NNNazaf8AAAAAMRfnPax7G9obU6Z1jt0HUk=; wxuin=55526051096880; rand_info=CAESIKsloR6OOQfNScl/od3IUTolQ+IDJ+lL3D+voKFdo+YE; slave_bizuin=3551612504; data_bizuin=3551612504; bizuin=3551612504; data_ticket=sgs/wtO+sEHa7jh3cfXZ0ZF8WaQeDGssMz3D54tZTc14GB3ph9SKb3Z2KhJ8oQpj; slave_sid=MElfckd0bUFpN25mS0ZiNnZ0eGYxY0dSUXhxTEJIbGI2RVNvR0EwekMzYWFGcHN5RV9QcWZpQ3ZVcXMzZDk4V052Vl9tQU5qaGFBdVN2dVptNFp2UjJzRXFYV1ZEY1pOVlJFTHZrcER1VFhJTmRORHY3d1dsQ05GWlJyRktMOHFQakN5MEJEaUJ0QzlGaGY0; slave_user=gh_561fbcf46439; xid=8e22af45a7176c6a7601491d54fbc556; mm_lang=zh_CN; pgv_pvid=1684355930; ts_uid=307682454; personAgree_3551612504=true; _clck=3551612504|1|fym|0; mmad_session=8696760cfc07b0ed29d73f2d7dd5ddc00e279c7efba8475bf98ba35155ddefc74fff08c08de448a540b6c7bd3cd4e0e44dec9fac251cae842785c00ae05c39ff42c117f666cefc46088980f8bdda19bd379b5ea9ed87da62c199b20e21a2e23611de1c56c245721266e7088080fefde3; pgv_info=ssid=s2751831146; _clsk=1flpp3z|1755705092934|5|1|mp.weixin.qq.com/weheat-agent/payload/record"
    token = "2141231990"  # 需要填入有效的 token
    nickname = "深入人心"

    spider = WeChatSpider(cookie, token)

    try:
        articles = spider.fetch_articles_by_nickname(nickname)
        if not articles:
            print(f"未获取到公众号 {nickname} 的任何文章。")
            return

        # 将文章列表转换为 DataFrame
        df = pd.DataFrame(articles)

        # 导出到 Excel 文件
        excel_file = fr"C:\Users\<USER>\Desktop\{nickname}_articles.xlsx"
        df.to_excel(excel_file, index=False)  # 使用 utf-8-sig 以支持中文

        print(f"成功将文章导出到 {excel_file}")
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    main()