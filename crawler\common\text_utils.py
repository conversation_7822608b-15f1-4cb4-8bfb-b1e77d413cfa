from typing import List


def remove_nonvisible_chars(text: str) -> str:
    """移除不可见字符与多余的空白符（保持换行与空格的最小必要集）。"""
    import unicodedata
    return ''.join(
        c for c in text
        if (unicodedata.category(c) != 'Cn' and c not in (' ', '\n', '\r'))
    )


def _paragraph_contains_keywords(paragraph: List[str], keywords: List[str]) -> bool:
    paragraph_text = ' '.join(paragraph)
    return any(keyword in paragraph_text for keyword in keywords)


def filter_content(text: str, paragraph_keywords: List[str] | None = None) -> str:
    """按段落关键字过滤内容，命中则丢弃该段落。"""
    if not paragraph_keywords:
        return text

    lines = [line.strip() for line in text.split('\n')]
    filtered_lines: List[str] = []
    current_paragraph: List[str] = []

    for line in lines:
        if not line.strip():
            if not _paragraph_contains_keywords(current_paragraph, paragraph_keywords):
                filtered_lines.extend(current_paragraph)
            current_paragraph = []
        else:
            current_paragraph.append(line)

    if not _paragraph_contains_keywords(current_paragraph, paragraph_keywords):
        filtered_lines.extend(current_paragraph)

    return '\n\n'.join(filtered_lines) + '\n\n'


