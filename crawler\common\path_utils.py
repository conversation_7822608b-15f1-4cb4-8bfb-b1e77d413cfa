from __future__ import annotations

from pathlib import Path
from typing import Union
import json


def get_project_root() -> Path:
    """返回项目根目录路径（通过本文件所在的 common/ 上一级推断）。"""
    return Path(__file__).resolve().parent.parent


def get_app_base_dir() -> Path:
    """返回 apps/ 基础目录路径。"""
    return get_project_root() / "apps"


def resolve_path_from_apps(*parts: Union[str, Path]) -> str:
    """从 apps/ 目录拼接路径并返回字符串路径。"""
    return str(get_app_base_dir().joinpath(*parts))


def resolve_path_from_root(*parts: Union[str, Path]) -> str:
    """从项目根目录拼接路径并返回字符串路径。"""
    return str(get_project_root().joinpath(*parts))


def resolve_path_from_dir(dirname: Union[str, Path], *parts: Union[str, Path]) -> str:
    """
    从项目根下的某个一级目录（如 'credentials'、'extensions'）拼接路径并返回字符串路径。
    """
    return str((get_project_root() / str(dirname)).joinpath(*parts))


def get_theme_config_path() -> Path:
    """返回主题配置文件路径，默认位于 apps/theme_config.json。"""
    return get_app_base_dir() / "theme_config.json"


def load_theme_config() -> dict:
    """加载主题配置，若不存在则返回默认值。"""
    path = get_theme_config_path()
    if path.exists():
        try:
            return json.loads(path.read_text(encoding="utf-8"))
        except Exception:
            pass
    return {"theme": "light"}


def save_theme_config(config: dict) -> None:
    """保存主题配置。"""
    path = get_theme_config_path()
    path.write_text(json.dumps(config, ensure_ascii=False, indent=2), encoding="utf-8")


