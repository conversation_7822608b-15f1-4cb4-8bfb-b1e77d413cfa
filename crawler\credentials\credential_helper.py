#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
微信公众号凭证获取助手
帮助用户更容易地获取和验证<PERSON>ie和Token
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import requests
import json
import time
from datetime import datetime

class CredentialHelper:
    def __init__(self, root):
        self.root = root
        self.root.title("微信公众号凭证获取助手")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="微信公众号凭证获取助手", font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 步骤说明
        steps_frame = ttk.LabelFrame(main_frame, text="获取步骤", padding="10")
        steps_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        steps_frame.columnconfigure(0, weight=1)
        
        steps_text = """1. 打开浏览器，访问：https://mp.weixin.qq.com/
2. 登录你的微信公众号账号
3. 按F12打开开发者工具，切换到"Network"标签页
4. 在公众平台中点击任意菜单（如"素材管理"）
5. 在Network中找到发送到mp.weixin.qq.com的请求
6. 点击该请求，复制Cookie和Token到下方输入框
7. 点击"验证凭证"确保有效性"""
        
        steps_label = ttk.Label(steps_frame, text=steps_text, justify=tk.LEFT)
        steps_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # Cookie输入
        ttk.Label(main_frame, text="Cookie:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.cookie_entry = tk.Text(main_frame, height=4, width=60)
        self.cookie_entry.grid(row=2, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Token输入
        ttk.Label(main_frame, text="Token:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.token_entry = ttk.Entry(main_frame, width=60)
        self.token_entry.grid(row=3, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=20)
        
        ttk.Button(button_frame, text="验证凭证", command=self.validate_credentials).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空输入", command=self.clear_inputs).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="复制到主程序", command=self.copy_to_main).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="详细指南", command=self.show_detailed_guide).pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        self.status_var = tk.StringVar(value="请输入Cookie和Token")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, foreground="blue")
        status_label.grid(row=5, column=0, columnspan=3, pady=5)
        
        # 日志区域
        ttk.Label(main_frame, text="验证日志:").grid(row=6, column=0, sticky=tk.W, pady=(20, 5))
        
        log_frame = ttk.Frame(main_frame)
        log_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置主框架的行权重
        main_frame.rowconfigure(7, weight=1)
        
    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_inputs(self):
        """清空输入"""
        self.cookie_entry.delete("1.0", tk.END)
        self.token_entry.delete(0, tk.END)
        self.status_var.set("已清空输入")
        
    def validate_credentials(self):
        """验证凭证"""
        cookie = self.cookie_entry.get("1.0", tk.END).strip()
        token = self.token_entry.get().strip()
        
        if not cookie:
            messagebox.showerror("错误", "请输入Cookie")
            return
            
        if not token:
            messagebox.showerror("错误", "请输入Token")
            return
            
        self.status_var.set("正在验证...")
        
        # 在新线程中验证
        def validate_thread():
            try:
                from wechat_spider_gui import WeChatSpider
                spider = WeChatSpider(cookie, token, progress_callback=self.log_message)
                is_valid, message = spider.test_credentials()
                
                if is_valid:
                    self.root.after(0, lambda: self.status_var.set("✅ 凭证有效"))
                    self.root.after(0, lambda: messagebox.showinfo("验证成功", "Cookie和Token有效！\n\n可以点击'复制到主程序'将凭证复制到剪贴板"))
                else:
                    self.root.after(0, lambda: self.status_var.set("❌ 凭证无效"))
                    self.root.after(0, lambda: messagebox.showerror("验证失败", f"凭证验证失败：{message}\n\n请重新获取Cookie和Token"))
                    
            except Exception as e:
                self.root.after(0, lambda: self.status_var.set("❌ 验证异常"))
                self.root.after(0, lambda: messagebox.showerror("验证异常", f"验证过程中发生异常：{str(e)}"))
                
        thread = threading.Thread(target=validate_thread)
        thread.daemon = True
        thread.start()
        
    def copy_to_main(self):
        """复制凭证到剪贴板"""
        cookie = self.cookie_entry.get("1.0", tk.END).strip()
        token = self.token_entry.get().strip()
        
        if not cookie or not token:
            messagebox.showerror("错误", "请先输入Cookie和Token")
            return
            
        # 复制到剪贴板
        credentials_text = f"Cookie: {cookie}\nToken: {token}"
        self.root.clipboard_clear()
        self.root.clipboard_append(credentials_text)
        
        messagebox.showinfo("复制成功", "凭证已复制到剪贴板！\n\n请在主程序中粘贴使用")
        
    def show_detailed_guide(self):
        """显示详细指南"""
        guide_window = tk.Toplevel(self.root)
        guide_window.title("详细获取指南")
        guide_window.geometry("800x600")
        guide_window.resizable(True, True)
        
        text_frame = ttk.Frame(guide_window, padding="10")
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        guide_content = """详细的Cookie和Token获取指南

=== 准备工作 ===
1. 确保你有微信公众号的管理权限
2. 使用Chrome、Firefox或Edge浏览器
3. 确保网络连接正常

=== 详细步骤 ===

步骤1：登录微信公众平台
- 打开浏览器，访问：https://mp.weixin.qq.com/
- 使用微信扫码或账号密码登录
- 确保成功进入公众号管理后台

步骤2：打开开发者工具
- 按F12键打开开发者工具
- 或者右键点击页面，选择"检查"/"审查元素"
- 切换到"Network"（网络）标签页

步骤3：触发网络请求
- 在公众号后台点击任意菜单，如：
  * 素材管理
  * 用户管理  
  * 消息管理
  * 统计
- 观察Network标签页中的请求

步骤4：找到正确的请求
- 在Network中查找发送到"mp.weixin.qq.com"的请求
- 通常是GET或POST请求
- 点击该请求查看详情

步骤5：提取Cookie
- 在请求详情的"Headers"部分
- 找到"Request Headers"
- 复制"Cookie"字段的完整内容
- Cookie通常很长，包含多个键值对

步骤6：提取Token
- 在同一个请求中查看URL
- 或者在"Query String Parameters"中
- 找到"token"参数
- 复制token的值（通常是一长串字符）

=== 常见问题 ===

Q: 找不到mp.weixin.qq.com的请求？
A: 尝试刷新页面或点击不同的菜单项

Q: Cookie很长，如何确保复制完整？
A: 可以双击Cookie值全选，然后Ctrl+C复制

Q: Token在哪里找？
A: Token通常在URL参数中，形如：?token=1234567890

Q: 复制后如何验证？
A: 在本工具中粘贴后点击"验证凭证"

=== 注意事项 ===
- Cookie和Token会定期过期，需要重新获取
- 不要在多个地方同时使用同一账号
- 保护好你的凭证信息，不要泄露给他人
- 如果验证失败，请重新获取最新的凭证

=== 故障排除 ===
1. 确保完整复制了Cookie（不要遗漏任何字符）
2. 确保Token格式正确（纯数字字符串）
3. 尝试重新登录后再获取
4. 检查网络连接是否正常
5. 确保浏览器没有阻止JavaScript"""

        text_widget = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD, width=90, height=35)
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(tk.END, guide_content)
        text_widget.config(state=tk.DISABLED)
        
        ttk.Button(guide_window, text="关闭", command=guide_window.destroy).pack(pady=10)

def main():
    """主函数"""
    root = tk.Tk()
    app = CredentialHelper(root)
    root.mainloop()

if __name__ == "__main__":
    main()
