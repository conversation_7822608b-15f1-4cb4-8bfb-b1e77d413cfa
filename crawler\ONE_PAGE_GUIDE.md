# 一页合并说明（快速上手与关键要点）

## 1) 安装与环境（以 apps/ 为工作目录）

- Python 3.10+（已知可用）
- 安装依赖：

```bash
pip install -r requirements.txt
# 可选：多格式文件转换
pip install markitdown[all]
```

## 2) 入口与使用

- 所有命令建议先执行：

```bash
cd apps
```

- GUI 转换器（推荐）

```bash
python enhanced_markdown_converter.py --gui
```

- CLI 转换：

```bash
# 文件
python enhanced_markdown_converter.py --file path/to/file.pdf --output ./output
# URL
python enhanced_markdown_converter.py --url https://example.com/article --output ./output
```

- 微信爬虫 GUI：

```bash
python wechat_spider_gui.py
```

- 启动器（工具集一键入口）：

```bash
python 启动器.py
```

## 3) 配置与输出

- 配置文件：
  - `enhanced_converter_config.json`（内容过滤关键词等）
  - `wechat_gui_config.json`（Cookie/Token、搜索方式、输出目录）
- 输出目录：统一写入到 `输出目录/MD文档/`（重名自动跳过）

## 4) 凭证获取与验证（简要）

1. 登录 <https://mp.weixin.qq.com> 并进入公众平台；F12 打开开发者工具 → Network
2. 复制任一请求的完整 Cookie
3. 从页面 URL 或请求参数中找到 token=...
4. 在 GUI 中粘贴并验证；过期后重新获取

## 5) 关键能力与差异

- 转换器：网页/微信文章/多格式文件 → Markdown；并发、过滤、去重
- MarkItDown 优先，失败回退微信专用解析（两者统一去重与写入策略）
- 统一 Session + Retry，更稳定（公共模块：`common/http.py`）
- 公共文本工具：`common/text_utils.py`（清洗与段落过滤）

## 6) 常见问题与故障排除（精简）

- 未安装 MarkItDown：仅支持基础网页/少量文件；安装 `markitdown[all]` 可扩展能力
- 重复文件：同名将跳过；修改标题或输出目录后重试
- 网络错误：已自动重试；仍失败请稍后再试
- Cookie/Token 过期：重新登录公众平台获取最新值；避免多端同时登录
- Excel 保存失败（爬虫 GUI）：确认写权限、关闭同名文件、磁盘空间充足

## 7) 安全与合规

- 仅用于学习研究；遵守平台条款与法律法规
- `wechat_gui_config.json` 等包含敏感信息，请勿提交到仓库或对外分享

## 8) 项目结构（关键）

- `enhanced_markdown_converter.py`：主转换入口（GUI/CLI）
- `common/`：统一会话与文本工具
- `wechat/`：微信解析通用逻辑
- `wechat_spider_gui.py`：爬虫 GUI（文章列表 → Excel）
- `2md.py`、`2MD_*`：旧版/兼容脚本（已部分委托公共模块）

---

本页合并并精简了以下文档的核心信息：

- `GUI使用说明.md`、`使用指南.md`
- `GUI优化说明.md`、`优化说明.md`、`新功能说明.md`
- `扩展故障排除步骤.md`、`浏览器扩展Cookie问题修复说明.md`
- `自动获取安全说明.md`、`搜索请求Cookie捕获说明.md`
- `停止功能修复说明.md`、`凭证过期分析报告.md`
- `完整自动化方案.md`、`解决方案总结.md`、`VSCode使用指南.md`

如需历史细节，请查看上述文件的提交记录。
