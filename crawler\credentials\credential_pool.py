#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
凭证池管理系统
支持多个凭证轮换使用，减少单个凭证的使用频率
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import os
from datetime import datetime, timedelta
import threading
import time

class CredentialPool:
    def __init__(self, root):
        self.root = root
        self.root.title("凭证池管理系统")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        self.credentials = []  # 凭证列表
        self.current_index = 0  # 当前使用的凭证索引
        # 使用统一路径工具解析凭证池文件路径
        from common.path_utils import resolve_path_from_dir
        self.config_file = resolve_path_from_dir("credentials", "credential_pool.json")
        
        self.create_widgets()
        self.load_credentials()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔄 凭证池管理系统", 
                               font=('Microsoft YaHei', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 说明
        info_label = ttk.Label(main_frame, 
                              text="通过轮换使用多个凭证，可以大大减少单个凭证的使用频率，延长有效期",
                              font=('Microsoft YaHei', 9), foreground='gray')
        info_label.pack(pady=(0, 15))
        
        # 凭证列表区域
        list_frame = ttk.LabelFrame(main_frame, text="凭证列表", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        
        # 创建表格
        columns = ('序号', '名称', '状态', '最后使用', '使用次数')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)
        
        # 设置列标题
        for col in columns:
            self.tree.heading(col, text=col)
            
        # 设置列宽
        self.tree.column('序号', width=50)
        self.tree.column('名称', width=150)
        self.tree.column('状态', width=80)
        self.tree.column('最后使用', width=120)
        self.tree.column('使用次数', width=80)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 操作按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Button(button_frame, text="添加凭证", command=self.add_credential).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="编辑凭证", command=self.edit_credential).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="删除凭证", command=self.delete_credential).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="验证凭证", command=self.validate_credential).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="获取当前凭证", command=self.get_current_credential).pack(side=tk.LEFT, padx=(0, 10))
        
        # 状态区域
        status_frame = ttk.LabelFrame(main_frame, text="当前状态", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 15))
        
        self.status_var = tk.StringVar(value="凭证池为空")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.pack()
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=6)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def log_message(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def load_credentials(self):
        """加载凭证"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.credentials = data.get('credentials', [])
                    self.current_index = data.get('current_index', 0)
                    
                self.refresh_list()
                self.log_message(f"加载了 {len(self.credentials)} 个凭证")
            else:
                self.log_message("未找到凭证池配置文件")
        except Exception as e:
            self.log_message(f"加载凭证失败: {str(e)}")
            
    def save_credentials(self):
        """保存凭证"""
        try:
            data = {
                'credentials': self.credentials,
                'current_index': self.current_index
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            self.log_message("凭证池已保存")
        except Exception as e:
            self.log_message(f"保存凭证失败: {str(e)}")
            
    def refresh_list(self):
        """刷新凭证列表"""
        # 清空现有项目
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # 添加凭证项目
        for i, cred in enumerate(self.credentials):
            status = "✅ 当前" if i == self.current_index else "⏸️ 待用"
            if cred.get('invalid', False):
                status = "❌ 失效"
                
            last_used = cred.get('last_used', '从未')
            if last_used != '从未':
                try:
                    last_used = datetime.fromisoformat(last_used).strftime("%m-%d %H:%M")
                except:
                    pass
                    
            self.tree.insert('', tk.END, values=(
                i + 1,
                cred.get('name', f'凭证{i+1}'),
                status,
                last_used,
                cred.get('use_count', 0)
            ))
            
        # 更新状态
        if self.credentials:
            current_cred = self.credentials[self.current_index] if self.current_index < len(self.credentials) else None
            if current_cred:
                self.status_var.set(f"当前使用: {current_cred.get('name', '未命名')} (第{self.current_index + 1}个)")
            else:
                self.status_var.set("当前凭证索引无效")
        else:
            self.status_var.set("凭证池为空")
            
    def add_credential(self):
        """添加凭证"""
        self.edit_credential_dialog()
        
    def edit_credential(self):
        """编辑凭证"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要编辑的凭证")
            return
            
        item = self.tree.item(selection[0])
        index = int(item['values'][0]) - 1
        self.edit_credential_dialog(index)
        
    def edit_credential_dialog(self, index=None):
        """凭证编辑对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("编辑凭证" if index is not None else "添加凭证")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 主框架
        main_frame = ttk.Frame(dialog, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 名称
        ttk.Label(main_frame, text="凭证名称:").pack(anchor=tk.W)
        name_var = tk.StringVar()
        name_entry = ttk.Entry(main_frame, textvariable=name_var, width=50)
        name_entry.pack(fill=tk.X, pady=(0, 10))
        
        # Cookie
        ttk.Label(main_frame, text="Cookie:").pack(anchor=tk.W)
        cookie_text = tk.Text(main_frame, height=6, width=70)
        cookie_text.pack(fill=tk.X, pady=(0, 10))
        
        # Token
        ttk.Label(main_frame, text="Token:").pack(anchor=tk.W)
        token_var = tk.StringVar()
        token_entry = ttk.Entry(main_frame, textvariable=token_var, width=50)
        token_entry.pack(fill=tk.X, pady=(0, 10))
        
        # 如果是编辑模式，填充现有数据
        if index is not None and index < len(self.credentials):
            cred = self.credentials[index]
            name_var.set(cred.get('name', ''))
            cookie_text.insert('1.0', cred.get('cookie', ''))
            token_var.set(cred.get('token', ''))
            
        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        def save_credential():
            name = name_var.get().strip()
            cookie = cookie_text.get('1.0', tk.END).strip()
            token = token_var.get().strip()
            
            if not name or not cookie or not token:
                messagebox.showerror("错误", "请填写完整信息")
                return
                
            cred_data = {
                'name': name,
                'cookie': cookie,
                'token': token,
                'created_time': datetime.now().isoformat(),
                'use_count': 0,
                'invalid': False
            }
            
            if index is not None:
                # 编辑模式
                old_cred = self.credentials[index]
                cred_data['use_count'] = old_cred.get('use_count', 0)
                cred_data['last_used'] = old_cred.get('last_used', '从未')
                self.credentials[index] = cred_data
                self.log_message(f"已更新凭证: {name}")
            else:
                # 添加模式
                self.credentials.append(cred_data)
                self.log_message(f"已添加凭证: {name}")
                
            self.save_credentials()
            self.refresh_list()
            dialog.destroy()
            
        ttk.Button(button_frame, text="保存", command=save_credential).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT)
        
    def delete_credential(self):
        """删除凭证"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要删除的凭证")
            return
            
        item = self.tree.item(selection[0])
        index = int(item['values'][0]) - 1
        name = self.credentials[index].get('name', '未命名')
        
        if messagebox.askyesno("确认删除", f"确定要删除凭证 '{name}' 吗？"):
            del self.credentials[index]
            
            # 调整当前索引
            if self.current_index >= len(self.credentials):
                self.current_index = max(0, len(self.credentials) - 1)
                
            self.save_credentials()
            self.refresh_list()
            self.log_message(f"已删除凭证: {name}")
            
    def validate_credential(self):
        """验证凭证"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要验证的凭证")
            return
            
        item = self.tree.item(selection[0])
        index = int(item['values'][0]) - 1
        
        def validate_thread():
            try:
                cred = self.credentials[index]
                from wechat_spider_gui import WeChatSpider
                spider = WeChatSpider(cred['cookie'], cred['token'])
                
                is_valid, message = spider.test_credentials()
                
                # 更新凭证状态
                cred['invalid'] = not is_valid
                cred['last_check'] = datetime.now().isoformat()
                
                self.save_credentials()
                self.root.after(0, self.refresh_list)
                
                if is_valid:
                    self.root.after(0, lambda: messagebox.showinfo("验证成功", f"凭证 '{cred['name']}' 有效"))
                    self.root.after(0, lambda: self.log_message(f"凭证 '{cred['name']}' 验证成功"))
                else:
                    self.root.after(0, lambda: messagebox.showerror("验证失败", f"凭证 '{cred['name']}' 已失效: {message}"))
                    self.root.after(0, lambda: self.log_message(f"凭证 '{cred['name']}' 验证失败: {message}"))
                    
            except Exception as e:
                self.root.after(0, lambda: messagebox.showerror("验证异常", f"验证过程中发生异常: {str(e)}"))
                self.root.after(0, lambda: self.log_message(f"验证异常: {str(e)}"))
                
        threading.Thread(target=validate_thread, daemon=True).start()
        
    def get_current_credential(self):
        """获取当前凭证"""
        if not self.credentials:
            messagebox.showwarning("提示", "凭证池为空")
            return None
            
        if self.current_index >= len(self.credentials):
            self.current_index = 0
            
        current_cred = self.credentials[self.current_index]
        
        # 更新使用统计
        current_cred['use_count'] = current_cred.get('use_count', 0) + 1
        current_cred['last_used'] = datetime.now().isoformat()
        
        # 轮换到下一个凭证
        self.current_index = (self.current_index + 1) % len(self.credentials)
        
        self.save_credentials()
        self.refresh_list()
        
        # 复制到剪贴板
        credentials_text = f"Cookie: {current_cred['cookie']}\nToken: {current_cred['token']}"
        self.root.clipboard_clear()
        self.root.clipboard_append(credentials_text)
        
        messagebox.showinfo("获取成功", f"已获取凭证 '{current_cred['name']}'\n\n凭证已复制到剪贴板\n下次将自动轮换到下一个凭证")
        self.log_message(f"已使用凭证: {current_cred['name']}")
        
        return current_cred

def main():
    """主函数"""
    root = tk.Tk()
    app = CredentialPool(root)
    root.mainloop()

if __name__ == "__main__":
    main()
