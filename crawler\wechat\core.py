from __future__ import annotations

import os
import re
import time
from typing import Optional, <PERSON>ple

from bs4 import BeautifulSoup
import markdownify

from common.text_utils import remove_nonvisible_chars, filter_content


def get_title_with_retry(session, url: str, max_retries: int = 3, timeout: int = 10) -> Tuple[Optional[str], Optional[BeautifulSoup]]:
    retries = 0
    while retries < max_retries:
        try:
            response = session.get(url, timeout=timeout)
            response.raise_for_status()

            soup = BeautifulSoup(response.text, 'lxml')
            title_element = soup.find('h1', id="activity-name") or \
                            soup.find('h2', class_="rich_media_title") or \
                            soup.find('h1', class_="article-title")
            if title_element:
                title = title_element.text.strip()
                if title:
                    return title, soup

            raise AttributeError("Title element not found")

        except Exception as e:  # noqa: PIE786 keep broad here to retry any fetch failure
            retries += 1
            if retries < max_retries:
                time.sleep(2 ** retries)
            else:
                return None, None


def convert_wechat_article(content_soup: BeautifulSoup,
                           title: str,
                           create_time: str = "",
                           keep_images: bool = False,
                           apply_filters: bool = True,
                           paragraph_keywords: Optional[list[str]] = None) -> tuple[str, str]:
    """将微信文章 DOM 转为 Markdown。

    - keep_images=False: 删除所有 img 标签
    - apply_filters=True: 按段落关键字过滤
    """
    if not keep_images:
        for img in content_soup.find_all('img'):
            img.decompose()

    markdown_content = markdownify.markdownify(str(content_soup))
    markdown_content = '\n'.join([line + '\n' for line in markdown_content.split('\n') if line.strip()])

    clean_title = remove_nonvisible_chars(title)
    markdown = f'# {clean_title}\n\n{create_time}\n\n{markdown_content}\n'
    markdown = re.sub('\xa0{1,}', '\n', markdown)
    markdown = re.sub(r'\]\(http([^)]*)\)', lambda x: '](http' + x.group(1).replace(' ', '%20') + ')', markdown)

    if apply_filters:
        markdown = filter_content(markdown, paragraph_keywords or [])

    return markdown, clean_title


