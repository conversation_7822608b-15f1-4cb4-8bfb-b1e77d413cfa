#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
微信爬虫核心方法补充
用于恢复缺失的方法
"""

import time
import random
import requests
import json
from datetime import datetime

def add_missing_methods_to_spider():
    """
    返回需要添加到WeChatSpider类的方法代码
    """
    
    methods_code = '''
    def test_credentials(self):
        """
        测试Cookie和Token是否有效
        
        :return: (是否有效, 消息)
        """
        try:
            self.log_progress("正在验证Cookie和Token...")
            self.log_progress(f"Cookie长度: {len(self.cookie)}, Token长度: {len(self.token)}")
            
            # 添加请求锁保护
            with self._request_lock:
                pass
            
            # 方式1: 测试搜索API
            test_url = "https://mp.weixin.qq.com/cgi-bin/searchbiz"
            params = {
                **self.base_params,
                "action": "search_biz",
                "query": "测试",
                "begin": 0,
                "count": 1,
                "ajax": "1",
            }
            
            self.log_progress("发送验证请求...")
            response = self.session.get(test_url, headers=self.headers, params=params, timeout=15)
            self.log_progress(f"收到响应，状态码: {response.status_code}")
            
            response.raise_for_status()
            
            try:
                data = response.json()
                self.log_progress("响应JSON解析成功")
            except ValueError as e:
                self.log_progress(f"JSON解析失败: {str(e)}")
                return False, "响应不是有效的JSON格式，可能Cookie或Token格式错误"
            
            if 'base_resp' in data:
                ret_code = data['base_resp'].get('ret')
                self.log_progress(f"API返回码: {ret_code}")
                
                if ret_code == 200013:
                    return False, "Cookie/Token 已过期或无效"
                elif ret_code == 0:
                    self.log_progress("✅ Cookie和Token验证成功")
                    return True, "验证成功"
                elif ret_code == 200003:
                    # 权限不足，但凭证可能仍然有效
                    self.log_progress("搜索API权限不足，但凭证基本有效")
                    return True, "验证成功（权限受限）"
                else:
                    return False, f"API错误码: {ret_code}"
            else:
                return False, "响应格式异常"
                
        except Exception as e:
            self.log_progress(f"❌ 验证错误: {str(e)}")
            return False, f"验证过程中发生错误: {str(e)}"
    
    def log_progress(self, message):
        """记录进度信息"""
        if self.progress_callback:
            self.progress_callback(message)
        print(message)
    
    def smart_delay(self, base_delay=2, max_delay=8, api_type="default"):
        """
        智能延时，根据请求频率动态调整
        
        :param base_delay: 基础延时（秒）
        :param max_delay: 最大延时（秒）
        :param api_type: API类型，用于区分不同接口
        """
        with self._request_lock:
            current_time = time.time()
            time_since_last = current_time - self._last_request_time
            
            # 如果距离上次请求时间太短，增加延时
            if time_since_last < self._min_request_interval:
                wait_time = self._min_request_interval - time_since_last
                self.log_progress(f"⏱️ 频率控制，等待 {wait_time:.1f} 秒...")
                time.sleep(wait_time)
            
            # 添加随机延时，避免固定模式
            random_delay = base_delay + random.uniform(0, min(base_delay, max_delay - base_delay))
            self.log_progress(f"⏱️ 智能延时 {random_delay:.1f} 秒（{api_type}）...")
            time.sleep(random_delay)
            
            # 更新最后请求时间
            self._last_request_time = time.time()
    
    def refresh_session(self):
        """
        刷新会话，通过访问主页来保持会话活跃
        """
        try:
            self.log_progress("正在刷新会话...")
            main_url = "https://mp.weixin.qq.com/"
            response = self.session.get(main_url, headers=self.headers, timeout=10)
            if response.status_code == 200:
                self.log_progress("✅ 会话刷新成功")
                return True
            else:
                self.log_progress(f"⚠️ 会话刷新失败，状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_progress(f"⚠️ 会话刷新异常: {str(e)}")
            return False
    
    def get_fakeid(self, nickname, begin=0, count=5, retry_count=3):
        """
        根据公众号昵称搜索，获取其fakeid
        
        :param nickname: 公众号昵称
        :param begin: 开始位置
        :param count: 数量
        :param retry_count: 重试次数
        :return: 公众号的fakeid
        """
        self.log_progress(f"正在搜索公众号: {nickname}")
        
        for attempt in range(retry_count):
            if attempt > 0:
                self.log_progress(f"第{attempt + 1}次尝试搜索公众号...")
                # 在重试前刷新会话
                self.refresh_session()
                # 使用智能延时替代固定延时
                self.smart_delay(base_delay=2 + attempt, max_delay=5, api_type="search")
            
            search_url = "https://mp.weixin.qq.com/cgi-bin/searchbiz"
            params = {
                **self.base_params,
                "action": "search_biz",
                "query": nickname,
                "begin": begin,
                "count": count,
                "ajax": "1",
            }
            
            try:
                response = self.session.get(search_url, headers=self.headers, params=params, timeout=15)
                response.raise_for_status()
                
                try:
                    data = response.json()
                except ValueError:
                    if attempt < retry_count - 1:
                        self.log_progress("响应格式异常，准备重试...")
                        continue
                    else:
                        raise Exception("响应不是有效的JSON格式")
                
                # 检查API响应状态
                if 'base_resp' in data:
                    ret_code = data['base_resp'].get('ret')
                    if ret_code == 200013:
                        if attempt < retry_count - 1:
                            self.log_progress("⚠️ 检测到凭证可能过期，准备重试...")
                            self.smart_delay(base_delay=3, max_delay=8, api_type="retry")
                            continue
                        else:
                            raise Exception("Cookie/Token 已过期或无效，请重新获取")
                    elif ret_code == -1:
                        if attempt < retry_count - 1:
                            self.log_progress("⚠️ 系统繁忙，增加延时后重试...")
                            self.smart_delay(base_delay=5, max_delay=10, api_type="busy")
                            continue
                        else:
                            raise Exception("系统繁忙，请稍后重试")
                    elif ret_code != 0 and ret_code != 200003:
                        error_msg = f"搜索失败，错误码: {ret_code}"
                        if attempt < retry_count - 1:
                            self.log_progress(f"{error_msg}，准备重试...")
                            continue
                        else:
                            raise Exception(error_msg)
                    elif ret_code == 200003:
                        self.log_progress("搜索API权限不足，但尝试继续获取数据...")
                
                # 检查数据内容
                if "list" in data and data["list"]:
                    for item in data["list"]:
                        if item.get("nickname") == nickname:
                            fakeid = item.get("fakeid")
                            self.log_progress(f"找到公众号 {nickname}，FakeID: {fakeid}")
                            return fakeid
                
                if attempt < retry_count - 1:
                    self.log_progress(f"未找到公众号 {nickname}，准备重试...")
                    continue
                else:
                    self.log_progress(f"未找到公众号: {nickname}")
                    return None
                    
            except Exception as e:
                if attempt < retry_count - 1:
                    self.log_progress(f"搜索出错: {str(e)}，准备重试...")
                    continue
                else:
                    error_msg = f"获取公众号{nickname}的fakeid失败: {str(e)}"
                    self.log_progress(error_msg)
                    raise Exception(error_msg)
        
        return None
    
    def get_articles_page(self, fakeid, begin=0, count=5, retry_count=3):
        """
        获取单页文章，带重试机制
        
        :param fakeid: 公众号的fakeid
        :param begin: 开始位置
        :param count: 每页数量
        :param retry_count: 重试次数
        :return: (文章列表, 是否还有下一页)
        """
        for attempt in range(retry_count):
            if attempt > 0:
                self.log_progress(f"第{attempt + 1}次尝试获取文章...")
                # 重试前刷新会话
                self.refresh_session()
                # 使用智能延时，根据重试次数递增
                self.smart_delay(base_delay=3 + attempt * 2, max_delay=10, api_type="articles")
            
            art_url = "https://mp.weixin.qq.com/cgi-bin/appmsg"
            params = {
                **self.base_params,
                "query": "",
                "begin": begin,
                "count": count,
                "type": 9,
                "action": "list_ex",
                "fakeid": fakeid,
            }
            
            try:
                response = self.session.get(art_url, headers=self.headers, params=params, timeout=15)
                response.raise_for_status()
                
                try:
                    data = response.json()
                except ValueError as e:
                    if attempt < retry_count - 1:
                        self.log_progress("响应格式异常，准备重试...")
                        continue
                    else:
                        raise Exception(f"响应不是有效的JSON格式: {str(e)}")
                
                # 检查API响应状态
                if 'base_resp' in data:
                    ret_code = data['base_resp'].get('ret')
                    if ret_code == 200013:
                        if attempt < retry_count - 1:
                            self.log_progress("⚠️ 凭证可能过期，准备重试...")
                            self.smart_delay(base_delay=5, max_delay=10, api_type="retry")
                            continue
                        else:
                            raise Exception("Cookie/Token 已过期或无效，请重新获取")
                    elif ret_code == -1:
                        if attempt < retry_count - 1:
                            self.log_progress("⚠️ 系统繁忙，增加延时后重试...")
                            self.smart_delay(base_delay=8, max_delay=15, api_type="busy")
                            continue
                        else:
                            raise Exception("系统繁忙，请稍后重试")
                    elif ret_code != 0:
                        error_msg = f"获取文章失败，错误码: {ret_code}"
                        if attempt < retry_count - 1:
                            self.log_progress(f"{error_msg}，准备重试...")
                            self.smart_delay(base_delay=3, max_delay=8, api_type="error")
                            continue
                        else:
                            raise Exception(error_msg)
                
                # 解析文章列表
                if "app_msg_list" in data and data["app_msg_list"]:
                    articles_on_page = data["app_msg_list"]
                    articles = [
                        {
                            "标题": item.get("title"),
                            "链接": item.get("link"),
                            "发布时间": datetime.fromtimestamp(item.get("create_time", 0)).strftime("%Y-%m-%d %H:%M:%S") if item.get("create_time") else "未知"
                        }
                        for item in articles_on_page
                    ]
                    has_more = len(articles_on_page) >= count
                    return articles, has_more
                else:
                    # 没有文章列表
                    if begin == 0:  # 第一页就没有文章
                        if attempt < retry_count - 1:
                            self.log_progress("第一页未获取到文章，准备重试...")
                            continue
                        else:
                            raise Exception("未能获取到任何文章，可能是FakeID错误或权限不足")
                    else:
                        return [], False  # 后续页面没有文章，正常结束
                        
            except Exception as e:
                if "Cookie/Token 已过期" in str(e) or "系统繁忙" in str(e):
                    if attempt < retry_count - 1:
                        continue
                    else:
                        raise e
                else:
                    if attempt < retry_count - 1:
                        self.log_progress(f"获取文章出错: {str(e)}，准备重试...")
                        continue
                    else:
                        raise Exception(f"获取文章失败: {str(e)}")
        
        raise Exception("重试次数已用完，获取文章失败")
'''
    
    return methods_code

# 保存方法代码供参考
if __name__ == "__main__":
    code = add_missing_methods_to_spider()
    print("生成的方法代码已准备好")
    print(f"代码长度: {len(code)} 字符")
