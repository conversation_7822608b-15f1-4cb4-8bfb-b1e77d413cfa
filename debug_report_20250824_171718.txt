[2025-08-24 17:15:29] [INFO] 🚀 开始微信爬虫调试测试
[2025-08-24 17:15:29] [INFO] Token长度: 9
[2025-08-24 17:15:29] [INFO] Cookie长度: 808
[2025-08-24 17:15:29] [INFO] ============================================================
[2025-08-24 17:15:29] [INFO] 分析Cookie结构
[2025-08-24 17:15:29] [INFO] Cookie包含 18 个字段
[2025-08-24 17:15:29] [INFO]   ✅ wxuin: 55526...96880
[2025-08-24 17:15:29] [WARNING]   ❌ pass_ticket: 缺失
[2025-08-24 17:15:29] [WARNING]   ❌ wap_sid2: 缺失
[2025-08-24 17:15:29] [INFO]   ✅ uuid: 59f23...083c8
[2025-08-24 17:15:29] [WARNING]   ❌ ticket: 缺失
[2025-08-24 17:15:29] [WARNING]   ❌ ticket_id: 缺失
[2025-08-24 17:15:29] [INFO] ============================================================
[2025-08-24 17:15:29] [INFO] 开始测试凭证有效性
[2025-08-24 17:15:29] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:29] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:29] [SPIDER] 发送验证请求...
[2025-08-24 17:15:30] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:30] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:30] [SPIDER] API返回码: 0
[2025-08-24 17:15:30] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:30] [SUCCESS] ✅ 凭证初始验证通过
[2025-08-24 17:15:30] [INFO] 
测试请求频率限制...
[2025-08-24 17:15:30] [INFO] 测试间隔 0.5 秒的请求...
[2025-08-24 17:15:31] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:31] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:31] [SPIDER] 发送验证请求...
[2025-08-24 17:15:31] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:31] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:31] [SPIDER] API返回码: 0
[2025-08-24 17:15:31] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:32] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:32] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:32] [SPIDER] 发送验证请求...
[2025-08-24 17:15:33] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:33] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:33] [SPIDER] API返回码: 0
[2025-08-24 17:15:33] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:33] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:33] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:33] [SPIDER] 发送验证请求...
[2025-08-24 17:15:34] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:34] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:34] [SPIDER] API返回码: 0
[2025-08-24 17:15:34] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:34] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:34] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:34] [SPIDER] 发送验证请求...
[2025-08-24 17:15:35] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:35] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:35] [SPIDER] API返回码: 0
[2025-08-24 17:15:35] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:35] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:35] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:35] [SPIDER] 发送验证请求...
[2025-08-24 17:15:36] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:36] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:36] [SPIDER] API返回码: 0
[2025-08-24 17:15:36] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:36] [INFO]   间隔0.5秒: 成功5次, 失败0次
[2025-08-24 17:15:36] [INFO] 测试间隔 1 秒的请求...
[2025-08-24 17:15:37] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:37] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:37] [SPIDER] 发送验证请求...
[2025-08-24 17:15:37] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:37] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:37] [SPIDER] API返回码: 0
[2025-08-24 17:15:37] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:38] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:38] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:38] [SPIDER] 发送验证请求...
[2025-08-24 17:15:39] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:39] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:39] [SPIDER] API返回码: 0
[2025-08-24 17:15:39] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:40] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:40] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:40] [SPIDER] 发送验证请求...
[2025-08-24 17:15:40] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:40] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:40] [SPIDER] API返回码: 0
[2025-08-24 17:15:40] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:41] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:41] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:41] [SPIDER] 发送验证请求...
[2025-08-24 17:15:42] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:42] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:42] [SPIDER] API返回码: 0
[2025-08-24 17:15:42] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:43] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:43] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:43] [SPIDER] 发送验证请求...
[2025-08-24 17:15:44] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:44] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:44] [SPIDER] API返回码: 0
[2025-08-24 17:15:44] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:44] [INFO]   间隔1秒: 成功5次, 失败0次
[2025-08-24 17:15:44] [INFO] 测试间隔 2 秒的请求...
[2025-08-24 17:15:46] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:46] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:46] [SPIDER] 发送验证请求...
[2025-08-24 17:15:46] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:46] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:46] [SPIDER] API返回码: 0
[2025-08-24 17:15:46] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:48] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:48] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:48] [SPIDER] 发送验证请求...
[2025-08-24 17:15:49] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:49] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:49] [SPIDER] API返回码: 0
[2025-08-24 17:15:49] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:51] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:51] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:51] [SPIDER] 发送验证请求...
[2025-08-24 17:15:51] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:51] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:51] [SPIDER] API返回码: 0
[2025-08-24 17:15:51] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:53] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:53] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:53] [SPIDER] 发送验证请求...
[2025-08-24 17:15:54] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:54] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:54] [SPIDER] API返回码: 0
[2025-08-24 17:15:54] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:56] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:56] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:56] [SPIDER] 发送验证请求...
[2025-08-24 17:15:56] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:15:56] [SPIDER] 响应JSON解析成功
[2025-08-24 17:15:56] [SPIDER] API返回码: 0
[2025-08-24 17:15:56] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:15:56] [INFO]   间隔2秒: 成功5次, 失败0次
[2025-08-24 17:15:56] [INFO] 测试间隔 3 秒的请求...
[2025-08-24 17:15:59] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:15:59] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:15:59] [SPIDER] 发送验证请求...
[2025-08-24 17:16:00] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:16:00] [SPIDER] 响应JSON解析成功
[2025-08-24 17:16:00] [SPIDER] API返回码: 0
[2025-08-24 17:16:00] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:16:03] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:16:03] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:16:03] [SPIDER] 发送验证请求...
[2025-08-24 17:16:04] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:16:04] [SPIDER] 响应JSON解析成功
[2025-08-24 17:16:04] [SPIDER] API返回码: 0
[2025-08-24 17:16:04] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:16:07] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:16:07] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:16:07] [SPIDER] 发送验证请求...
[2025-08-24 17:16:07] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:16:07] [SPIDER] 响应JSON解析成功
[2025-08-24 17:16:07] [SPIDER] API返回码: 0
[2025-08-24 17:16:07] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:16:10] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:16:10] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:16:10] [SPIDER] 发送验证请求...
[2025-08-24 17:16:11] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:16:11] [SPIDER] 响应JSON解析成功
[2025-08-24 17:16:11] [SPIDER] API返回码: 0
[2025-08-24 17:16:11] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:16:14] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:16:14] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:16:14] [SPIDER] 发送验证请求...
[2025-08-24 17:16:14] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:16:14] [SPIDER] 响应JSON解析成功
[2025-08-24 17:16:14] [SPIDER] API返回码: 0
[2025-08-24 17:16:14] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:16:14] [INFO]   间隔3秒: 成功5次, 失败0次
[2025-08-24 17:16:14] [INFO] 测试间隔 5 秒的请求...
[2025-08-24 17:16:19] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:16:19] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:16:19] [SPIDER] 发送验证请求...
[2025-08-24 17:16:20] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:16:20] [SPIDER] 响应JSON解析成功
[2025-08-24 17:16:20] [SPIDER] API返回码: 0
[2025-08-24 17:16:20] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:16:25] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:16:25] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:16:25] [SPIDER] 发送验证请求...
[2025-08-24 17:16:25] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:16:25] [SPIDER] 响应JSON解析成功
[2025-08-24 17:16:25] [SPIDER] API返回码: 0
[2025-08-24 17:16:25] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:16:30] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:16:30] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:16:30] [SPIDER] 发送验证请求...
[2025-08-24 17:16:31] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:16:31] [SPIDER] 响应JSON解析成功
[2025-08-24 17:16:31] [SPIDER] API返回码: 0
[2025-08-24 17:16:31] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:16:36] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:16:36] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:16:36] [SPIDER] 发送验证请求...
[2025-08-24 17:16:36] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:16:36] [SPIDER] 响应JSON解析成功
[2025-08-24 17:16:36] [SPIDER] API返回码: 0
[2025-08-24 17:16:36] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:16:41] [SPIDER] 正在验证Cookie和Token...
[2025-08-24 17:16:41] [SPIDER] Cookie长度: 808, Token长度: 9
[2025-08-24 17:16:41] [SPIDER] 发送验证请求...
[2025-08-24 17:16:42] [SPIDER] 收到响应，状态码: 200
[2025-08-24 17:16:42] [SPIDER] 响应JSON解析成功
[2025-08-24 17:16:42] [SPIDER] API返回码: 0
[2025-08-24 17:16:42] [SPIDER] ✅ Cookie和Token验证成功
[2025-08-24 17:16:42] [INFO]   间隔5秒: 成功5次, 失败0次
[2025-08-24 17:16:42] [INFO] ============================================================
[2025-08-24 17:16:42] [INFO] 测试错误恢复机制
[2025-08-24 17:16:42] [INFO] 测试文章获取重试机制...
[2025-08-24 17:16:42] [INFO] 
尝试获取 测试公众号 的文章...
[2025-08-24 17:16:42] [SPIDER] 正在搜索公众号: 测试公众号
[2025-08-24 17:16:43] [SPIDER] 找到公众号 测试公众号，FakeID: MzIyMzMxOTkwNg==
[2025-08-24 17:16:43] [SUCCESS] ✅ 成功获取 测试公众号 的FakeID: MzIyMzMxOTkwNg==
[2025-08-24 17:16:43] [SPIDER] API响应状态: 0
[2025-08-24 17:16:43] [SPIDER] 第一页未获取到文章，准备重试...
[2025-08-24 17:16:43] [SPIDER] 第2次尝试获取文章...
[2025-08-24 17:16:43] [SPIDER] 正在刷新会话...
[2025-08-24 17:16:46] [SPIDER] ✅ 会话刷新成功
[2025-08-24 17:16:46] [SPIDER] 等待7秒后重试...
[2025-08-24 17:16:53] [SPIDER] API响应状态: 0
[2025-08-24 17:16:53] [SPIDER] 第一页未获取到文章，准备重试...
[2025-08-24 17:16:53] [SPIDER] 第3次尝试获取文章...
[2025-08-24 17:16:53] [SPIDER] 正在刷新会话...
[2025-08-24 17:16:56] [SPIDER] ✅ 会话刷新成功
[2025-08-24 17:16:56] [SPIDER] 等待9秒后重试...
[2025-08-24 17:17:05] [SPIDER] API响应状态: 0
[2025-08-24 17:17:05] [SPIDER] 完整API响应: {
  "app_msg_cnt": 0,
  "app_msg_list": [],
  "base_resp": {
    "err_msg": "ok",
    "ret": 0
  }
}
[2025-08-24 17:17:05] [ERROR] ❌ 测试失败: 未能获取到任何文章，可能是FakeID错误或权限不足
[2025-08-24 17:17:08] [INFO] 
尝试获取 人民日报 的文章...
[2025-08-24 17:17:08] [SPIDER] 正在搜索公众号: 人民日报
[2025-08-24 17:17:09] [SPIDER] 找到公众号 人民日报，FakeID: MjM5MjAxNDM4MA==
[2025-08-24 17:17:09] [SUCCESS] ✅ 成功获取 人民日报 的FakeID: MjM5MjAxNDM4MA==
[2025-08-24 17:17:09] [SPIDER] API响应状态: 0
[2025-08-24 17:17:09] [SUCCESS] ✅ 成功获取 3 篇文章
[2025-08-24 17:17:12] [INFO] 
尝试获取 新华社 的文章...
[2025-08-24 17:17:12] [SPIDER] 正在搜索公众号: 新华社
[2025-08-24 17:17:13] [SPIDER] 找到公众号 新华社，FakeID: MzA4NDI3NjcyNA==
[2025-08-24 17:17:13] [SUCCESS] ✅ 成功获取 新华社 的FakeID: MzA4NDI3NjcyNA==
[2025-08-24 17:17:13] [SPIDER] API响应状态: 0
[2025-08-24 17:17:13] [SUCCESS] ✅ 成功获取 2 篇文章
[2025-08-24 17:17:16] [INFO] ============================================================
[2025-08-24 17:17:16] [INFO] 测试并发请求
[2025-08-24 17:17:16] [INFO] 启动3个并发请求...
[2025-08-24 17:17:16] [SPIDER] [线程1] 正在验证Cookie和Token...
[2025-08-24 17:17:16] [SPIDER] [线程1] Cookie长度: 808, Token长度: 9
[2025-08-24 17:17:16] [SPIDER] [线程1] 发送验证请求...
[2025-08-24 17:17:17] [SPIDER] [线程2] 正在验证Cookie和Token...
[2025-08-24 17:17:17] [SPIDER] [线程2] Cookie长度: 808, Token长度: 9
[2025-08-24 17:17:17] [SPIDER] [线程2] 发送验证请求...
[2025-08-24 17:17:17] [SPIDER] [线程1] 收到响应，状态码: 200
[2025-08-24 17:17:17] [SPIDER] [线程1] 响应JSON解析成功
[2025-08-24 17:17:17] [SPIDER] [线程1] API返回码: 0
[2025-08-24 17:17:17] [SPIDER] [线程1] ✅ Cookie和Token验证成功
[2025-08-24 17:17:17] [SUCCESS] ✅ 线程1 验证成功
[2025-08-24 17:17:17] [SPIDER] [线程3] 正在验证Cookie和Token...
[2025-08-24 17:17:17] [SPIDER] [线程3] Cookie长度: 808, Token长度: 9
[2025-08-24 17:17:17] [SPIDER] [线程3] 发送验证请求...
[2025-08-24 17:17:18] [SPIDER] [线程2] 收到响应，状态码: 200
[2025-08-24 17:17:18] [SPIDER] [线程2] 响应JSON解析成功
[2025-08-24 17:17:18] [SPIDER] [线程2] API返回码: 0
[2025-08-24 17:17:18] [SPIDER] [线程2] ✅ Cookie和Token验证成功
[2025-08-24 17:17:18] [SUCCESS] ✅ 线程2 验证成功
[2025-08-24 17:17:18] [SPIDER] [线程3] 收到响应，状态码: 200
[2025-08-24 17:17:18] [SPIDER] [线程3] 响应JSON解析成功
[2025-08-24 17:17:18] [SPIDER] [线程3] API返回码: 0
[2025-08-24 17:17:18] [SPIDER] [线程3] ✅ Cookie和Token验证成功
[2025-08-24 17:17:18] [SUCCESS] ✅ 线程3 验证成功
[2025-08-24 17:17:18] [INFO] ============================================================
[2025-08-24 17:17:18] [INFO] 📊 测试报告总结
[2025-08-24 17:17:18] [INFO] ✅ 成功: 9
[2025-08-24 17:17:18] [INFO] ⚠️ 警告: 4
[2025-08-24 17:17:18] [INFO] ❌ 错误: 1