import requests
from bs4 import BeautifulSoup, NavigableString
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON>Executor, as_completed
from docx import Document
from docx.shared import Inches
from docx.oxml.ns import qn
import re
import time
from io import BytesIO
import os
from PIL import Image
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 创建输出目录
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'output')
os.makedirs(OUTPUT_DIR, exist_ok=True)

def sanitize_filename(filename):
    """
    清理文件名，移除不合法的字符。
    Args:
        filename (str): 原始文件名。
    Returns:
        str: 清理后的合法文件名。
    """
    return re.sub(r'[\/*?"<>|]', "", filename).strip()

def compress_image(image_data, max_size_kb=500):
    """
    压缩图片数据，使其大小不超过指定的KB数。

    Args:
        image_data (bytes): 原始图片数据。
        max_size_kb (int): 压缩后的最大大小（KB）。

    Returns:
        bytes: 压缩后的图片数据。
    """
    img = Image.open(BytesIO(image_data))
    
    # 如果是PNG格式且有透明通道，转换为RGB
    if img.mode in ('RGBA', 'LA') or (img.mode == 'P' and 'transparency' in img.info):
        bg = Image.new('RGB', img.size, 'white')
        if img.mode == 'P':
            img = img.convert('RGBA')
        bg.paste(img, mask=img.split()[3] if img.mode == 'RGBA' else None)
        img = bg

    # 计算当前大小
    temp_buffer = BytesIO()
    img.save(temp_buffer, format='JPEG', quality=95)
    current_size = len(temp_buffer.getvalue()) / 1024

    # 如果大小已经小于目标大小，直接返回
    if current_size <= max_size_kb:
        temp_buffer.seek(0)
        return temp_buffer.getvalue()

    # 二分法查找合适的压缩质量
    quality_low, quality_high = 5, 95
    while quality_low < quality_high:
        quality = (quality_low + quality_high) // 2
        temp_buffer = BytesIO()
        img.save(temp_buffer, format='JPEG', quality=quality)
        current_size = len(temp_buffer.getvalue()) / 1024

        if abs(current_size - max_size_kb) < 10:  # 如果大小接近目标值，可以接受
            break
        elif current_size > max_size_kb:
            quality_high = quality - 1
        else:
            quality_low = quality + 1

    temp_buffer.seek(0)
    return temp_buffer.getvalue()

def fetch_article_content(url, session):
    """
    获取微信文章的HTML内容。
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36',
    }
    response = session.get(url, headers=headers, timeout=20)
    response.raise_for_status()
    response.encoding = 'utf-8'
    return BeautifulSoup(response.text, 'lxml')

def process_content_elements(doc, content_div, session):
    """
    处理文章内容元素并添加到Word文档中。
    """
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36',
    }

    stop_text = "在上传至交流群的文件中"

    for element in content_div.find_all(['p', 'h2', 'h3', 'img', 'pre']):
        if element.name == 'p':
            text = element.get_text(strip=True)
            if stop_text in text:
                logging.info("找到结束标记，后续内容将不再写入文档。")
                break
        
        if element.name == 'pre':
            for br in element.find_all("br"):
                br.replace_with("\n")
            code_text = element.get_text(' ', strip=True)
            if code_text:
                para = doc.add_paragraph()
                run = para.add_run(code_text)
                font = run.font
                font.name = 'Consolas'
                rpr = run._element.get_or_add_rPr()
                rpr.rFonts.set(qn('w:eastAsia'), 'Consolas')
                para.style = 'Intense Quote'
        elif element.name == 'p':
            # 如果p标签在pre标签内，则跳过，避免重复处理
            if element.find_parent('pre'):
                continue
            text = element.get_text(strip=True)
            if text:
                doc.add_paragraph(text)
        elif element.name in ['h2', 'h3']:
            doc.add_heading(element.get_text(strip=True), level=int(element.name[1]))
        elif element.name == 'img':
            img_url = element.get('data-src') or element.get('src')
            if img_url and img_url.startswith('http'):
                try:
                    img_data = session.get(img_url, headers=headers, timeout=15).content
                    compressed_img_data = compress_image(img_data)
                    image_stream = BytesIO(compressed_img_data)
                    doc.add_picture(image_stream, width=Inches(5.5))
                    image_stream.close()
                    logging.info(f"成功处理图片: {img_url}")
                except requests.exceptions.RequestException as e:
                    logging.error(f"图片下载失败: {img_url}, 错误: {e}")
                except Exception as e:
                    logging.error(f"图片处理失败: {img_url}, 错误: {e}")

def create_word_document(soup, session):
    """
    创建并填充Word文档。
    """
    title_tag = soup.find('h1', class_=['rich_media_title', 'article_title'])
    title = title_tag.get_text(strip=True) if title_tag else '无标题'
    content_div = soup.find('div', class_=['rich_media_content', 'js_article_content'])
    if not content_div:
        raise ValueError("未找到正文内容")

    doc = Document()
    doc.styles['Normal'].font.name = '宋体'
    doc.styles['Normal']._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')

    doc.add_heading(title, level=1)
    date_tag = soup.find('em', id='publish_time')
    if date_tag:
        doc.add_paragraph(f"发布日期：{date_tag.get_text(strip=True)}")

    process_content_elements(doc, content_div, session)

    sanitized_title = sanitize_filename(title)
    output_filename = f"{sanitized_title}.docx"
    output_path = os.path.join(OUTPUT_DIR, output_filename)
    
    doc.save(output_path)
    logging.info(f"文章《{title}》已成功导出至 {output_path}")

def weixin_article_to_word(url):
    """
    将指定的微信公众号文章URL内容抓取并保存为Word文档。
    """
    try:
        with requests.Session() as session:
            soup = fetch_article_content(url, session)
            create_word_document(soup, session)
            return True
    except requests.exceptions.RequestException as e:
        logging.error(f"请求失败: {url}, 错误: {e}")
    except Exception as e:
        logging.error(f"处理失败: {url}, 错误: {e}")
    return False

def process_articles():
    """
    批量处理微信文章，将其转换为Word文档。
    用户可以输入多个链接，每行一个，输入空行结束。
    """
    print("请输入微信文章链接，每行一个，输入空行结束：")
    urls = []
    while True:
        url = input().strip()
        if not url:
            break
        urls.append(url)

    if not urls:
        print("未输入任何链接")
        return

    success_count = 0
    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_url = {executor.submit(weixin_article_to_word, url): url for url in urls}
        for i, future in enumerate(as_completed(future_to_url), 1):
            url = future_to_url[future]
            print(f"\n处理第 {i}/{len(urls)} 个链接: {url}")
            try:
                if future.result():
                    success_count += 1
            except Exception as exc:
                logging.error(f'{url} 生成时发生错误: {exc}')

    print(f"\n处理完成！成功: {success_count}, 失败: {len(urls) - success_count}")
    print(f"文档保存位置: {OUTPUT_DIR}")

if __name__ == "__main__":
    process_articles() 